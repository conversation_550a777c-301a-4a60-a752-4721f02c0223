<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Medication extends Model
{
    use HasFactory;

    const DISPATCH_METHOD_DISPENSE_PRO = 'Dispense Pro';
    const DISPATCH_METHOD_FAX = 'Fax Plus';

    //TABLE
    public $table = 'medications';

    //FILLABLES
    protected $fillable = [
        'name',
        'ndc',
        'is_active',
        'dispensepro_medication_name',
        'vial_quantity',
        'days_supply',
        'sig',
        'notes',
        'refills',
        'default_dispatch_method',
        'dispense_abbreviation',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function practices()
    {
        return $this->belongsToMany(Practice::class, 'practice_provider_medication', 'medication_id', 'practice_id')
            ->withPivot('provider_id')
            ->withTimestamps();
    }

    public function providers()
    {
        return $this->belongsToMany(User::class, 'practice_provider_medication', 'medication_id', 'provider_id')
            ->withPivot('practice_id')
            ->withTimestamps();
    }


    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}

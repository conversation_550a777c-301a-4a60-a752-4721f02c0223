<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Http\Requests\PracticeGenerateRequest;
use App\Http\Requests\PracticeStoreRequest;
use App\Models\Practice;
use App\Models\PracticeToken;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class PracticeController extends Controller
{
    public function index(Request $request)
    {
        $data = Practice::query();

        if ($request->has('search')) {
            $search = '%' . $request->search . '%';
            $data = $data->where(function ($query) use ($search) {
                $query->where('name', 'like', $search);
            });
        }

        if ($request->has('page')) {
            return response()->json(
                collect([
                    'message' => __('apiMessages.practice_list_returned'),
                    'status' => '1',
                ])->merge($data->paginate($request->has('per_page') ? $request->per_page : 10))
            );
        }

        return response()->json([
            'data' => $data->get(),
            'message' => __('apiMessages.practice_list_returned'),
            'status' => '1',
        ]);
    }

    public function store(PracticeStoreRequest $request)
    {
        $user = Auth::user();

        $practice = Practice::Create([
            'name' => $request->name,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'zip' => $request->zip,
            'phone' => $request->phone,
            'fax' => $request->fax,
        ]);

        $practice->save();

        // Create a personal access token for the practice
        $token = $practice->createToken('practice-token-' . $practice->id);

        // $tokenOnly = explode('|', $token->plainTextToken, 2)[1];
        $token = $token->plainTextToken;

        // Save the token to practice_token table
        PracticeToken::create([
            'practice_id' => $practice->id,
            'token' => $token,
        ]);

        return response()->json([
            'data' => $practice,
            'token' => $token,
            'message' => __('apiMessages.practice_created_successfully'),
            'status' => '1',
        ]);
    }

    public function generateToken(PracticeGenerateRequest $request)
    {
        $practice = Practice::findOrFail($request->practice_id);

        $token = $practice->createToken('practice-token-' . $practice->id);

        // $tokenOnly = explode('|', $token->plainTextToken, 2)[1];
        $token = $token->plainTextToken;

        PracticeToken::create([
            'practice_id' => $practice->id,
            'token' => $token,
        ]);

        return response()->json([
            'data' => [
                'practice_id' => $practice->id,
                'practice_name' => $practice->name,
                'token' => $token,
                'token_count' => $practice->practiceTokens()->count(),
            ],
            'message' => __('apiMessages.practice_token_generated_successfully'),
            'status' => '1',
        ]);
    }

    public function getPracticeByToken(Request $request)
    {
        $practice = Auth::user();
        

        return response()->json([
            'data' => $practice,
            'message' => __('apiMessages.practice_details_returned'),
            'status' => '1',
        ]);
    }
}

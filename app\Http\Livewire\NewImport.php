<?php

namespace App\Http\Livewire;

use App\Events\ScriptStatusChanged;
use App\Models\Import;
use App\Models\ImportFile;
use App\Models\Medication;
use App\Models\Patient;
use App\Models\ScriptTemplate;
use App\Models\User;
use App\Models\State;
use App\Services\LogService;
use App\Helpers\PatientHelper;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Livewire\Component;

class NewImport extends Component
{
    public $states = [];
    public $medications = [];
    public $providers = [];
    public $templates = [];
    public $selectedTemplate;
    public $provider;
    public $stateId;
    public $user;
    public $selectedPatientId;
    public ImportFile $importFile;
    public $matchingPatients = [];
    public $showPatientSuggestions = false;
    public $activeField = null;
    public $highlightedIndex = 0; // Track which suggestion is highlighted
    public $dob_display; // Display property for frontend (m-d-Y format)
    public $script_date_display; // Display property for frontend (m-d-Y format)

    public function mount(ImportFile $importFile)
    {
        $this->loadStates();
        $this->loadMedications();
        $this->loadProviders();
        $this->matchingPatients = []; // Initialize empty array


        // Set the import file and load the returnedByUser relationship
        $this->importFile = $importFile;

        // Load the returnedByUser relationship if the importFile has an ID
        if ($this->importFile->id) {
            $this->importFile->load('returnedByUser');
        }
        // Set default script date to today only if it's a new record (no existing script_date)
        if (!$this->importFile->script_date) {
            $this->importFile->script_date = Carbon::today()->format('Y-m-d');
            $this->script_date_display = Carbon::today()->format('m-d-Y');
        }

        // Set default ship_to value if not already set
        if (!$this->importFile->ship_to) {
            $this->importFile->ship_to = 'patient';
        }

        $this->user = Auth::user();

        $this->templates = ScriptTemplate::all();

        // Set dob_display from dob if it exists
        if ($this->importFile->dob) {
            $this->dob_display = Carbon::parse($this->importFile->dob)->format('m-d-Y');
        }

        // Set script_date_display from script_date if it exists
        if ($this->importFile->script_date) {
            $this->script_date_display = Carbon::parse($this->importFile->script_date)->format('m-d-Y');
        }
    }

    public function updatedSelectedTemplate($templateId)
    {
        if ($templateId) {
            $template = ScriptTemplate::find($templateId);
            if ($template) {
                $this->importFile->sig = $template->sig ?? '';
                $this->importFile->medication = $template->medication ?? '';
                $this->importFile->vial_quantity = $template->vial_quantity ?? '';
                $this->importFile->stregnth = $template->stregnth ?? '';
                $this->importFile->dosing = $template->dosing ?? '';
                $this->importFile->refills = $template->refills ?? '';
                $this->importFile->notes = $template->notes ?? '';
                $this->importFile->days_supply = $template->days_supply ?? '';
                $this->importFile->ship_to = $template->ship_to ? $template->ship_to : 'patient';
            }

            $this->dispatchBrowserEvent('template-dropdown-changed', [
                'medication' => $this->importFile->medication
            ]);
        } else {
            $this->importFile = new ImportFile();
        }
    }

    public function updatedImportFileMedication($medicationName)
    {
        if ($medicationName) {
            $medication = Medication::where('name', $medicationName)->first();
            if ($medication) {
                // Prefill form fields from medication data
                $this->importFile->vial_quantity = $medication->vial_quantity ?? '';
                $this->importFile->days_supply = $medication->days_supply ?? '';
                $this->importFile->sig = $medication->sig ?? '';
                $this->importFile->notes = $medication->notes ?? '';
                $this->importFile->refills = $medication->refills ?? '';
            }
        }

        // Recheck validation for medication-related fields
        $this->recheckMedicationValidation();
    }

    /**
     * Recheck validation for medication-related fields
     */
    public function recheckMedicationValidation()
    {
        // Clear validation errors for medication-related fields first
        $this->resetErrorBag([
            'importFile.medication',
            'importFile.refills',
            'importFile.vial_quantity',
            'importFile.days_supply',
            'importFile.sig',
            'importFile.notes',
            'importFile.ship_to'
        ]);

        // Validate medication-related fields
        $medicationFields = [
            'importFile.medication',
            'importFile.refills',
            'importFile.vial_quantity',
            'importFile.days_supply',
            'importFile.sig',
            'importFile.notes',
            'importFile.ship_to'
        ];

        foreach ($medicationFields as $field) {
            $this->validateOnly($field);
        }
    }

    public function searchPatients($search, $field = null)
    {
        // if ($this->selectedPatientId) {
        //     $this->matchingPatients = [];
        //     return;
        // }

        if (strlen($search) < 2) {
            $this->matchingPatients = [];
            return;
        }

        // $this->activeField = $field;

        $query = Patient::where('provider_id', $this->provider ?? $this->user->id);

        if ($field === 'first_name') {
            $query->where('first_name', 'like', $search . '%');
        } elseif ($field === 'last_name') {
            $query->where('last_name', 'like', $search . '%');
        } else {
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', $search . '%')
                    ->orWhere('last_name', 'like', $search . '%');
            });
        }

        $this->matchingPatients = $query->get();
    }

    public function updatedMatchingPatients()
    {
        $this->highlightedIndex = 0;
    }

    public function highlightPrevious()
    {
        if ($this->highlightedIndex > 0) {
            $this->highlightedIndex--;
        }
    }

    public function highlightNext()
    {
        if ($this->highlightedIndex < count($this->matchingPatients) - 1) {
            $this->highlightedIndex++;
        }
    }

    public function selectHighlightedPatient()
    {
        if (isset($this->matchingPatients[$this->highlightedIndex])) {
            $this->selectPatient($this->matchingPatients[$this->highlightedIndex]->id);
        }
    }

    public function getStateId($state)
    {
        $this->stateId = $state['id'];
    }

    public function selectPatient($patientId)
    {
        $this->selectedPatientId = $patientId;
        $this->matchingPatients = [];
        $this->activeField = null;
        $patient = Patient::find($patientId);
        if ($patient) {
            $this->importFile->first_name = $patient->first_name;
            $this->importFile->last_name = $patient->last_name;
            $this->importFile->dob = $patient->dob;
            $this->importFile->gender = $patient->gender;
            $this->importFile->address = $patient->address;
            $this->importFile->city = $patient->city;
            $this->importFile->state = optional($patient->state)->short_name;
            $this->importFile->zip = $patient->zip_code;
            $this->importFile->phone = $patient->phone_number;

            // Set dob_display from patient's dob
            if ($patient->dob) {
                $this->dob_display = Carbon::parse($patient->dob)->format('m-d-Y');
            }

            $this->matchingPatients = [];

            // we have to dispatch the dropdown-changed event to update the state selection
            // because we are using wire:ignore on select dropdown
            $this->dispatchBrowserEvent('dropdown-changed', [
                'state' => $this->importFile->state,
                'gender' => $this->importFile->gender,
            ]);
        }
    }

    public function resetPatientSelection()
    {
        $this->selectedPatientId = null;
        $this->matchingPatients = [];
        $this->importFile->first_name = '';
        $this->importFile->last_name = '';
        $this->importFile->dob = '';
        $this->dob_display = ''; // Clear display property as well
        $this->importFile->gender = '';
        $this->importFile->address = '';
        $this->importFile->city = '';
        $this->importFile->state = '';
        $this->importFile->zip = '';
        $this->importFile->phone = '';
        // Add any other fields you want to clear
    }

    public function checkAndResetPatientSelection($value, $field)
    {
        if (!empty($value)) {
            $this->searchPatients($value, $field);
        }
    }

    public function rules()
    {
        return [
            'script_date_display' => 'required|date_format:m-d-Y|before_or_equal:today',
            'importFile.last_name' => ['required', 'string', 'max:256', 'regex:/^[A-Za-z\s]+$/'],
            'importFile.first_name' => ['required', 'string', 'max:256', 'regex:/^[A-Za-z\s]+$/'],
            'dob_display' => 'required|date_format:m-d-Y|before:today',
            'importFile.gender' => 'required|string',
            'importFile.address' => ['required', 'string', 'max:256', 'regex:/^[A-Za-z0-9\s,\.\-]+$/'],
            'importFile.city' => 'required|max:256|regex:/^[A-Za-z\s]+$/',
            'importFile.state' => 'required',
            'importFile.zip' => ['required', 'string', 'regex:/^\d{5}(-\d{4})?$/'],
            'importFile.phone' => 'nullable|string|max:15',
            'importFile.medication' => 'required|string|max:255',
            'importFile.refills' => 'required|numeric|min:0|max:24',
            'importFile.vial_quantity' => 'required|numeric|min:1|max:32',
            'importFile.days_supply' => 'required|numeric|min:28|max:366',
            'importFile.sig' => 'nullable|string|max:1024',
            'importFile.notes' => 'nullable|string|max:1024',
            'importFile.ship_to' => 'required|exists:ship_to,name',
            'provider' => Rule::requiredIf($this->user->role != User::ROLE_PROVIDER),
        ];
    }

    public function messages()
    {
        return [
            'script_date_display.required' => 'The script date is required.',
            'script_date_display.date_format' => 'The script date must be in MM-DD-YYYY format.',
            'script_date_display.before_or_equal' => 'The script date cannot be in the future.',
            'dob_display.required' => 'The date of birth field is required.',
            'dob_display.date_format' => 'The date of birth must be in MM-DD-YYYY format.',
            'dob_display.before' => 'The date of birth must be a date before today.',
            'importFile.first_name.regex' => 'The first name may only contain alphabets and spaces.',
            'importFile.last_name.regex' => 'The last name may only contain letters and spaces.',
            'importFile.address.regex' => 'The address may only contain letters, numbers, spaces, commas, periods, and hyphens. no special characters.',
            'importFile.zip.regex' => 'The ZIP code must be in the format 12345 or 12345-6789.',
            'importFile.city.regex' => 'The city may only contain letters and spaces.',
            'importFile.city.max' => 'The city may not be greater than 256 characters.',
        ];
    }

    public function loadStates()
    {
        $this->states = State::orderBy('name')->get();
    }

    public function loadProviders()
    {
        $this->providers = User::where('role', User::ROLE_PROVIDER)->get();
    }

    public function loadMedications()
    {
        $this->medications = Medication::where('is_active', true)->get();
    }

    public function savePrescription()
    {
        $this->validate();

        // Convert script_date_display (m-d-Y) to script_date (Y-m-d) for saving
        if ($this->script_date_display) {
            $this->importFile->script_date = Carbon::createFromFormat('m-d-Y', $this->script_date_display)->format('Y-m-d');
        }

        // Convert dob_display (m-d-Y) to dob (Y-m-d) for saving
        if ($this->dob_display) {
            $this->importFile->dob = Carbon::createFromFormat('m-d-Y', $this->dob_display)->format('Y-m-d');
        }

        // Convert state name to ID if needed
        if ($this->stateId === null && !empty($this->importFile->state)) {
            $state = State::where('short_name', $this->importFile->state)->first();
            if ($state) {
                $this->stateId = $state->id;
            }
        }

        $patientResult = PatientHelper::findUpdateOrCreate([
            'provider_id' => $this->provider ?? $this->user->id,
            'first_name' => $this->importFile->first_name,
            'last_name' => $this->importFile->last_name,
            'dob' => $this->importFile->dob,
            'state' => $this->importFile->state,
            'phone_number' => $this->importFile->phone,
            'gender' => $this->importFile->gender,
            'city' => $this->importFile->city,
            'zip_code' => $this->importFile->zip,
            'address' => $this->importFile->address,
        ]);

        $existingPatient = $patientResult['patient'];
        $this->importFile->patient_id = $existingPatient->id;

        $isNew = !$this->importFile->id;
        $action = $isNew ? 'create' : 'update';

        if ($isNew) {
            // Create a new import record

            $user = $this->user;

            $user_id = ($user->role === User::ROLE_PROVIDER) ? $user->id : $this->provider;

            $import = Import::create([
                'file_name' => 'Manual Entry',
                'user_id' => $user_id,
            ]);



            // Set the import_id and other required fields
            $this->importFile->import_id = $import->id;
            $this->importFile->status = ImportFile::STATUS_NEW;
            $this->importFile->number = 1;
            if ($this->importFile->ship_to == null) {
                $this->importFile->ship_to = 'patient';
            }
        } else {
            $import = $this->importFile->import;
            if (!$this->importFile->is_eligible_for_signing) {
                $this->importFile->is_eligible_for_signing = true;
            }
        }

        // Default values if nothing set
        if (!$this->importFile->refills) {
            $this->importFile->refills = 0;
        }
        if (!$this->importFile->vial_quantity) {
            $this->importFile->vial_quantity = 1;
        }
        if (!$this->importFile->days_supply) {
            $this->importFile->days_supply = 28;
        }


        // Get original values for change tracking (only for updates)
        $originalValues = [];
        if (!$isNew && $this->importFile && method_exists($this->importFile, 'getOriginal')) {
            $originalValues = LogService::getModelAttributes($this->importFile->getOriginal(), [
                'first_name',
                'last_name',
                'dob',
                'gender',
                'address',
                'city',
                'state',
                'zip',
                'phone',
                'medication',
                'stregnth',
                'dosing',
                'refills',
                'vial_quantity',
                'days_supply',
                'sig',
                'notes',
                'ship_to',
            ]);
        }

        // Create storage path (DRY improvement)
        $storagePath = 'public/prescriptions/' . $import->id;
        Storage::makeDirectory($storagePath);

        // Save prescription (must be done in both create and update)
        $this->importFile->save();
        $prescription = $this->importFile;

        // Get user and state info
        $user = ($this->user->role === User::ROLE_PROVIDER) ? $this->user : $this->importFile->import->user;
        $userState = null;
        $doctorName = 'Dr. April';

        if ($user) {
            $doctorName = $user->printed_name ?? ($user->first_name . ' ' . $user->last_name);
            if ($user->state_id) {
                $userState = State::find($user->state_id);
            }
        }

        // Format dates
        $scriptDateFormatted = $this->importFile->script_date;
        if ($scriptDateFormatted && !is_string($scriptDateFormatted)) {
            try {
                $scriptDateFormatted = Carbon::parse($scriptDateFormatted)->format('m/d/Y');
            } catch (\Exception) {
                // keep original
            }
        }

        $dobFormatted = $this->importFile->dob;
        if ($dobFormatted && !is_string($dobFormatted)) {
            try {
                $dobFormatted = Carbon::parse($dobFormatted)->format('m/d/Y');
            } catch (\Exception) {
                // keep original
            }
        }

        // Prepare PDF data
        $data = [
            $scriptDateFormatted,
            $this->importFile->last_name,
            $this->importFile->first_name,
            $dobFormatted,
            $this->importFile->gender,
            $this->importFile->address,
            $this->importFile->city,
            $this->importFile->state,
            $this->importFile->zip,
            $this->importFile->phone,
            $this->importFile->medication,
            $this->importFile->refills,
            $this->importFile->vial_quantity,
            $this->importFile->days_supply,
            $this->importFile->sig,
            $this->importFile->notes,
            $this->importFile->ship_to ? $this->importFile->ship_to : 'patient',
        ];

        $pdf = PDF::loadView('pdf.new-prescription', [
            'data' => $data,
            'isPdfDownload' => true,
            'user' => $user,
            'userState' => $userState,
            'doctorName' => $doctorName,
            'isSigned' => false,
        ]);
        $pdf->setPaper('letter');

        // Save PDF
        $fileName = 'prescription_' . $prescription->id . '.pdf';
        $filePath = $storagePath . '/' . $fileName;
        Storage::put($filePath, $pdf->output());

        // Update record with file info
        $prescription->update([
            'file_name' => $fileName,
            'file_path' => $filePath,
        ]);

        // Create medication if needed
        Medication::firstOrCreate(['name' => $this->importFile->medication]);

        // Log script creation or editing
        if ($isNew) {
            LogService::logScriptCreated([
                'patient_name' => $prescription->first_name . ' ' . $prescription->last_name,
                'medication' => $prescription->medication,
                'script_id' => $prescription->id,
                'status' => $prescription->status
            ]);

            $this->importFile = new ImportFile();
            $this->importFile->script_date = Carbon::today()->format('Y-m-d');
            $this->script_date_display = Carbon::today()->format('m-d-Y');
        } else {
            // Get new values after saving
            $newValues = LogService::getModelAttributes($prescription, [
                'first_name',
                'last_name',
                'dob',
                'gender',
                'address',
                'city',
                'state',
                'zip',
                'phone',
                'medication',
                'stregnth',
                'dosing',
                'refills',
                'vial_quantity',
                'days_supply',
                'sig',
                'notes',
                'ship_to',
            ]);

            LogService::logScriptEdited([
                'patient_name' => $prescription->first_name . ' ' . $prescription->last_name,
                'medication' => $prescription->medication,
                'script_id' => $prescription->id,
                'status' => $prescription->status
            ], $originalValues, $newValues);
        }

        session()->flash('message', __('messages.script_saved_successfully'));

        return $action === 'update'
            ? redirect()->route('scripts.ready-to-sign')
            : redirect()->route('excel.view-pdf', ['id' => $prescription->id]);
    }

    public function saveAndSignPrescription()
    {
        $this->validate();
        $importFile = $this->importFile;

        if (!$importFile) {
            session()->flash('error-message', 'Prescription not found.');
            return;
        }

        // Delete existing PDF if present
        if ($importFile->file_path && Storage::exists($importFile->file_path)) {
            Storage::delete($importFile->file_path);
        }

        // Fetch associated user
        $user = optional($importFile->import)->user ?? User::find(optional($importFile->import)->user_id);
        $userState = $user?->state_id ? State::find($user->state_id) : null;
        $doctorName = $user?->printed_name ?? ($user?->first_name . ' ' . $user?->last_name ?? 'Dr. April');

        // Format fields
        $data = [
            $importFile->script_date ? Carbon::parse($importFile->script_date)->format('m/d/Y') : '',
            $importFile->last_name,
            $importFile->first_name ?? '',
            $importFile->dob ? Carbon::parse($importFile->dob)->format('m/d/Y') : '',
            $importFile->gender,
            $importFile->address,
            $importFile->city,
            $importFile->state,
            $importFile->zip,
            $importFile->phone,
            $importFile->medication,
            $importFile->refills,
            $importFile->vial_quantity,
            $importFile->days_supply,
            $importFile->sig ?? '',
            $importFile->notes ?? '',
            $importFile->ship_to ? $importFile->ship_to : 'patient',
        ];

        // Signature image path
        $signatureImagePath = $user?->signature
            ? storage_path('app/public/' . $user->signature)
            : null;

        if ($signatureImagePath && !file_exists($signatureImagePath)) {
            $signatureImagePath = null;
        }

        // Signed timestamp (client > session > fallback)
        $signedAt = $this->resolveSignedAt();

        // Update file status and save
        $importFile->status = ImportFile::STATUS_PENDING_APPROVAL;
        $importFile->signed_at = $signedAt;
        $importFile->save();

        // Dispatch status change event
        if ($currentUser = Auth::user()) {
            event(new ScriptStatusChanged([$importFile->toArray()], $currentUser));

            Log::info('ScriptStatusChanged event dispatched', [
                'user_id' => $currentUser->id,
                'user_name' => $currentUser->first_name . ' ' . $currentUser->last_name,
                'import_file_id' => $importFile->id
            ]);
        }

        // Generate PDF
        $formattedSignedAt = $importFile->signed_at
            ? Carbon::parse($importFile->signed_at)->format('m/d/Y h:i A')
            : now()->format('m/d/Y h:i A');

        Log::info('Generating signed PDF', [
            'import_file_id' => $importFile->id,
            'signed_at_db' => $importFile->signed_at,
            'formatted_signed_at' => $formattedSignedAt,
        ]);

        $pdf = PDF::loadView('pdf.new-prescription', [
            'data' => $data,
            'isPdfDownload' => true,
            'user' => $user,
            'userState' => $userState,
            'doctorName' => $doctorName,
            'isSigned' => true,
            'userSignature' => $signatureImagePath,
            'signed_at' => $formattedSignedAt,
            'ip_address' => request()->ip(),
        ]);

        $pdf->setOption('isPhpEnabled', true)
            ->setOption('isHtml5ParserEnabled', true)
            ->setOption('isRemoteEnabled', false)
            ->setPaper('letter');

        Storage::put($importFile->file_path, $pdf->output());

        return redirect()
            ->route('scripts.ready-to-sign')
            ->with('success-message', 'Scripts signed and sent for approval successfully.');
    }

    private function resolveSignedAt(): Carbon
    {
        $deviceTime = session('device_time');

        if ($deviceTime) {
            try {
                $signedAt = Carbon::createFromFormat('Y-m-d H:i:s O', $deviceTime);
                Log::info('Using device time from session for signed_at in archive', [
                    'timestamp' => $deviceTime
                ]);
                return $signedAt;
            } catch (\Exception $e) {
                Log::error('Failed to parse device time from session in archive', [
                    'timestamp' => $deviceTime,
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('Using current server time for signed_at in archive');
        return Carbon::now();
    }


    public function updatedScriptDateDisplay()
    {
        // Clear validation errors when user starts typing
        $this->resetErrorBag('script_date_display');

        // Convert script_date_display to script_date format for internal consistency
        if ($this->script_date_display) {
            try {
                $this->importFile->script_date = Carbon::createFromFormat('m-d-Y', $this->script_date_display)->format('Y-m-d');
            } catch (\Exception $e) {
                // If parsing fails, keep the original value
                // The validation will catch invalid formats
            }
        } else {
            $this->importFile->script_date = '';
        }
    }

    public function updatedDobDisplay()
    {
        // Clear validation errors when user starts typing
        $this->resetErrorBag('dob_display');

        // Convert dob_display to dob format for internal consistency
        if ($this->dob_display) {
            try {
                $this->importFile->dob = Carbon::createFromFormat('m-d-Y', $this->dob_display)->format('Y-m-d');
            } catch (\Exception $e) {
                // If parsing fails, keep the original value
                // The validation will catch invalid formats
            }
        } else {
            $this->importFile->dob = '';
        }
    }

    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);

        // Emit contentChanged event to re-initialize Select2 after property updates
        $this->emit('contentChanged');
    }

    public function render()
    {
        $user = Auth::user();

        if ($user->role === User::ROLE_PROVIDER) {
            return view('livewire.new-import');
        } else {
            return view('scripts.admin.new-import');
        }
    }
}

<div wire:poll.1000ms="pollProgress">
    <div class="container">
        <div class="mt-3">
            <?php if($title): ?>
            <h2 class="text-lg font-semibold text-gray-800">
                🛠️ Currently Processing: <span class="text-blue-600"><?php echo e($title ?? 'No Active Job'); ?></span>
            </h2>
            <?php endif; ?>
            <p class="text-sm text-gray-600 mt-1">
                Progress: <strong><?php echo e($processed); ?></strong> of <strong><?php echo e($total); ?></strong> tasks completed
            </p>
            <div class="progress">
                <div class="progress-bar" role="progressbar"
                    style="width: <?php echo e(($total > 0) ? ($processed / $total) * 100 : 0); ?>%;">
                    <?php echo e(intval(($processed / max($total, 1)) * 100)); ?>%
                </div>
            </div>
        </div>

        <table class="table table-bordered mt-5 p-5">
            <thead>
                <tr>
                    <td>Id</td>
                    <td>Title</td>
                    <td>Total</td>
                    <td>Status</td>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td><?php echo e($loop->iteration); ?></td>
                    <td><?php echo e($job->title); ?></td>
                    <td><?php echo e($job->total); ?></td>
                    <td>
                        <?php if($loop->iteration === 1): ?>
                        <span>🟢</span> RUNNING
                        <?php else: ?>
                        <span>🟡</span> Will start soon
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="5" class="text-center">No queue found</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
        <?php if(session()->has('message')): ?>
        <div class="alert alert-success mt-3">
            <?php echo e(session('message')); ?>

        </div>
        <?php endif; ?>
    </div>

</div><?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/livewire/scripts-panel.blade.php ENDPATH**/ ?>
[2025-08-04 06:44:26] local.ERROR: Form save error: SQLSTATE[22007]: Invalid datetime format: 1292 Incorrect date value: '01/11/1990' for column `newlife`.`patients`.`dob` at row 1 (Connection: mysql, SQL: insert into `patients` (`provider_id`, `first_name`, `last_name`, `dob`, `gender`, `city`, `state_id`, `zip_code`, `phone_number`, `address`, `updated_at`, `created_at`) values (3, Exercitation ipsa s, Cupidatat consequatu, 01/11/1990, M, Ut occaecat ullamco , 27, 12345, , Nobis quam consequat, 2025-08-04 06:44:26, 2025-08-04 06:44:26))  
[2025-08-04 06:46:07] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:16:07 +0630","user_id":1} 
[2025-08-04 07:03:26] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:33:25 +0630","user_id":1} 
[2025-08-04 07:03:53] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:33:52 +0630","user_id":1} 
[2025-08-04 07:06:34] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:36:34 +0630","user_id":1} 
[2025-08-04 07:06:37] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:36:37 +0630","user_id":1} 
[2025-08-04 07:09:25] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:39:24 +0630","user_id":1} 
[2025-08-04 07:10:05] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:40:05 +0630","user_id":1} 
[2025-08-04 07:11:26] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:41:26 +0630","user_id":1} 
[2025-08-04 07:13:16] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:43:15 +0630","user_id":1} 
[2025-08-04 07:13:17] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:43:17 +0630","user_id":1} 
[2025-08-04 07:14:00] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:43:59 +0630","user_id":1} 
[2025-08-04 07:14:04] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:44:04 +0630","user_id":1} 
[2025-08-04 07:14:35] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:44:35 +0630","user_id":1} 
[2025-08-04 07:14:46] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:44:46 +0630","user_id":1} 
[2025-08-04 07:19:33] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-04 07:19:47] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:49:47 +0630","user_id":1} 
[2025-08-04 07:20:02] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:50:02 +0630","user_id":3} 
[2025-08-04 07:20:06] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:50:05 +0630","user_id":3} 
[2025-08-04 07:20:07] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:50:07 +0630","user_id":3} 
[2025-08-04 07:21:39] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:51:38 +0630","user_id":3} 
[2025-08-04 07:22:27] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:52:27 +0630","user_id":1} 
[2025-08-04 07:22:31] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:52:31 +0630","user_id":3} 
[2025-08-04 07:22:58] local.INFO: Device time stored in session {"device_time":"2025-08-04 12:52:58 +0630","user_id":3} 
[2025-08-04 08:39:27] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:09:27 +0630","user_id":3} 
[2025-08-04 08:39:30] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:09:29 +0630","user_id":3} 
[2025-08-04 08:39:32] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:09:32 +0630","user_id":3} 
[2025-08-04 08:40:22] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:10:22 +0630","user_id":3} 
[2025-08-04 08:41:41] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:11:41 +0630","user_id":3} 
[2025-08-04 08:44:02] local.ERROR: syntax error, unexpected end of file, expecting "elseif" or "else" or "endif" {"view":{"view":"C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\resources\\views\\livewire\\patient-form\\patient-form.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#705</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"14 characters\">Create Patient</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","has_back":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/patients</span>\"
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","livewire_component":"<pre class=sf-dump id=sf-dump-623466966 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"20 characters\">patient.patient-form</span>\"
</pre><script>Sfdump(\"sf-dump-623466966\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","livewire_data":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>page_title</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Create Patient</span>\"
  \"<span class=sf-dump-key>patient</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Patient
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Patient</span></span> {<a class=sf-dump-ref>#738</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>
    #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>
    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">original</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">provider_id</span>\"
      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">first_name</span>\"
      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">last_name</span>\"
      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"3 characters\">dob</span>\"
      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">gender</span>\"
      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"4 characters\">city</span>\"
      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"8 characters\">state_id</span>\"
      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"8 characters\">zip_code</span>\"
      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"12 characters\">phone_number</span>\"
      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"7 characters\">address</span>\"
    </samp>]
    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
    </samp>]
    +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">patients</span>\"
  </samp>}
</samp>]
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":3,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): syntax error, unexpected end of file, expecting \"elseif\" or \"else\" or \"endif\" at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\resources\\views\\livewire\\patient-form\\patient-form.blade.php:100)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\PROJECTS\\\\Max...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\PROJECTS\\\\Max...', Array)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#64 {main}

[previous exception] [object] (ParseError(code: 0): syntax error, unexpected end of file, expecting \"elseif\" or \"else\" or \"endif\" at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\framework\\views\\a42a02d2f49317f637922fe0d4ace8ec.php:119)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Patient\\PatientForm->Livewire\\ComponentConcerns\\{closure}()
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Patient\\PatientForm), Object(Livewire\\Response))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(113): Livewire\\LifecycleManager->initialDehydrate()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('patient.patient...', Array)
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\framework\\views\\7456b463d7b5950cdd336c9c6bbc531e.php(12): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\PROJECTS\\\\Max...')
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\PROJECTS\\\\Max...', Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#75 {main}
"} 
[2025-08-04 08:44:21] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:14:21 +0630","user_id":3} 
[2025-08-04 08:45:05] local.ERROR: Form save error: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'provider_id' cannot be null (Connection: mysql, SQL: insert into `patients` (`provider_id`, `first_name`, `last_name`, `dob`, `state_id`, `updated_at`, `created_at`) values (?, Satvik, Santoki, 1990-01-01, ?, 2025-08-04 08:45:05, 2025-08-04 08:45:05))  
[2025-08-04 08:45:10] local.ERROR: Form save error: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'provider_id' cannot be null (Connection: mysql, SQL: insert into `patients` (`provider_id`, `first_name`, `last_name`, `dob`, `state_id`, `updated_at`, `created_at`) values (?, Satvik, Santoki, 1990-01-01, ?, 2025-08-04 08:45:10, 2025-08-04 08:45:10))  
[2025-08-04 08:45:32] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'provider_id' cannot be null (Connection: mysql, SQL: insert into `patients` (`provider_id`, `first_name`, `last_name`, `dob`, `state_id`, `updated_at`, `created_at`) values (?, Satvik, Santoki, 1990-01-01, ?, 2025-08-04 08:45:32, 2025-08-04 08:45:32)) {"userId":3,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'provider_id' cannot be null (Connection: mysql, SQL: insert into `patients` (`provider_id`, `first_name`, `last_name`, `dob`, `state_id`, `updated_at`, `created_at`) values (?, Satvik, Santoki, 1990-01-01, ?, 2025-08-04 08:45:32, 2025-08-04 08:45:32)) at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `pa...', Array, Object(Closure))
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `pa...', Array, Object(Closure))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `pa...', Array, 'id')
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `pa...', Array, 'id')
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Patient))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(App\\Models\\Patient), Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Helpers\\PatientHelper.php(38): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Livewire\\Patient\\PatientForm.php(224): App\\Helpers\\PatientHelper::findOrCreatePatient(NULL, 'Satvik', 'Santoki', '1990-01-01')
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Livewire\\Patient\\PatientForm.php(211): App\\Http\\Livewire\\Patient\\PatientForm->createPatient()
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Patient\\PatientForm->submit()
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('submit', Array, Object(Closure))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Patient\\PatientForm), Object(Livewire\\Request))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('patient.patient...')
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#72 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'provider_id' cannot be null at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:45)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(45): PDOStatement->execute()
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `pa...', Array)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `pa...', Array, Object(Closure))
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `pa...', Array, Object(Closure))
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `pa...', Array, 'id')
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `pa...', Array, 'id')
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Patient))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(App\\Models\\Patient), Object(Closure))
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Helpers\\PatientHelper.php(38): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Livewire\\Patient\\PatientForm.php(224): App\\Helpers\\PatientHelper::findOrCreatePatient(NULL, 'Satvik', 'Santoki', '1990-01-01')
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Livewire\\Patient\\PatientForm.php(211): App\\Http\\Livewire\\Patient\\PatientForm->createPatient()
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Patient\\PatientForm->submit()
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('submit', Array, Object(Closure))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Patient\\PatientForm), Object(Livewire\\Request))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('patient.patient...')
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#74 {main}
"} 
[2025-08-04 09:00:53] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:30:53 +0630","user_id":3} 
[2025-08-04 09:01:22] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:31:21 +0630","user_id":1} 
[2025-08-04 09:01:24] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:31:23 +0630","user_id":1} 
[2025-08-04 09:03:13] local.ERROR: Failed to send welcome email to provider: Call to a member function job() on int  
[2025-08-04 09:03:14] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:33:14 +0630","user_id":1} 
[2025-08-04 09:03:21] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:33:20 +0630","user_id":1} 
[2025-08-04 09:03:22] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:33:21 +0630","user_id":1} 
[2025-08-04 09:03:56] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:33:55 +0630","user_id":1} 
[2025-08-04 09:04:27] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:34:27 +0630","user_id":1} 
[2025-08-04 09:04:28] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:34:28 +0630","user_id":1} 
[2025-08-04 09:04:29] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:34:29 +0630","user_id":1} 
[2025-08-04 09:04:55] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:34:54 +0630","user_id":1} 
[2025-08-04 09:09:25] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:39:25 +0630","user_id":1} 
[2025-08-04 09:09:26] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:39:26 +0630","user_id":1} 
[2025-08-04 09:09:28] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:39:27 +0630","user_id":1} 
[2025-08-04 09:11:06] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:41:06 +0630","user_id":1} 
[2025-08-04 09:12:26] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:42:25 +0630","user_id":1} 
[2025-08-04 09:13:05] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:43:04 +0630","user_id":1} 
[2025-08-04 09:14:33] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:44:32 +0630","user_id":1} 
[2025-08-04 09:14:35] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:44:34 +0630","user_id":1} 
[2025-08-04 09:14:56] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:44:56 +0630","user_id":1} 
[2025-08-04 09:15:39] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:45:39 +0630","user_id":1} 
[2025-08-04 09:15:43] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:45:43 +0630","user_id":1} 
[2025-08-04 09:15:47] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:45:47 +0630","user_id":1} 
[2025-08-04 09:15:52] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:45:52 +0630","user_id":1} 
[2025-08-04 09:17:39] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:47:38 +0630","user_id":1} 
[2025-08-04 09:18:25] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:48:25 +0630","user_id":1} 
[2025-08-04 09:19:45] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:49:45 +0630","user_id":1} 
[2025-08-04 09:19:56] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:49:55 +0630","user_id":1} 
[2025-08-04 09:19:57] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:49:57 +0630","user_id":1} 
[2025-08-04 09:20:01] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:50:00 +0630","user_id":1} 
[2025-08-04 09:29:16] local.INFO: Device time stored in session {"device_time":"2025-08-04 14:59:15 +0630","user_id":1} 
[2025-08-04 09:33:47] local.INFO: Device time stored in session {"device_time":"2025-08-04 15:03:46 +0630","user_id":1} 
[2025-08-04 09:35:49] local.INFO: Device time stored in session {"device_time":"2025-08-04 15:05:49 +0630","user_id":1} 
[2025-08-04 09:35:52] local.INFO: Device time stored in session {"device_time":"2025-08-04 15:05:51 +0630","user_id":1} 
[2025-08-04 09:35:56] local.INFO: Device time stored in session {"device_time":"2025-08-04 15:05:56 +0630","user_id":1} 
[2025-08-04 09:36:17] local.INFO: Device time stored in session {"device_time":"2025-08-04 15:06:16 +0630","user_id":1} 
[2025-08-04 09:36:19] local.INFO: Device time stored in session {"device_time":"2025-08-04 15:06:19 +0630","user_id":1} 
[2025-08-04 09:36:22] local.INFO: Device time stored in session {"device_time":"2025-08-04 15:06:22 +0630","user_id":1} 
[2025-08-04 09:46:04] local.INFO: Device time stored in session {"device_time":"2025-08-04 15:16:03 +0630","user_id":1} 
[2025-08-04 10:29:28] local.INFO: Device time stored in session {"device_time":"2025-08-04 15:59:28 +0630","user_id":1} 
[2025-08-04 12:03:58] local.ERROR: Missing required parameter for [Route: users.edit] [URI: users/{provider}/edit] [Missing parameter: provider]. {"view":{"view":"C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\resources\\views\\scripts\\index-all.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#678</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-382668805 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"11 characters\">All scripts</span>\"
</pre><script>Sfdump(\"sf-dump-382668805\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","providers":"<pre class=sf-dump id=sf-dump-385810132 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#714</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#717</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:27</span> [ &#8230;27]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:27</span> [ &#8230;27]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:23</span> [ &#8230;23]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#718</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:27</span> [ &#8230;27]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:27</span> [ &#8230;27]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:23</span> [ &#8230;23]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-385810132\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","clinic_names":"<pre class=sf-dump id=sf-dump-899607739 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\Collection</span> {<a class=sf-dump-ref>#716</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Aashish Clinic</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">Search in</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-899607739\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","medications":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#720</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Medication
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Medication</span></span> {<a class=sf-dump-ref>#723</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:9</span> [ &#8230;9]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">medications</span>\"
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Medication
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Medication</span></span> {<a class=sf-dump-ref>#724</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:9</span> [ &#8230;9]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">medications</span>\"
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Missing required parameter for [Route: users.edit] [URI: users/{provider}/edit] [Missing parameter: provider]. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Exceptions\\UrlGenerationException.php:35)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUrlGenerator.php(90): Illuminate\\Routing\\Exceptions\\UrlGenerationException::forMissingParameters(Object(Illuminate\\Routing\\Route), Array)
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php(500): Illuminate\\Routing\\RouteUrlGenerator->to(Object(Illuminate\\Routing\\Route), Array, true)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php(469): Illuminate\\Routing\\UrlGenerator->toRoute(Object(Illuminate\\Routing\\Route), Array, true)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('users.edit', Array, true)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\resources\\views\\scripts\\index-all.blade.php(272): route('users.edit', Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\PROJECTS\\\\Max...')
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\PROJECTS\\\\Max...', Array)
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\AdminMiddleware.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#71 {main}

[previous exception] [object] (Illuminate\\Routing\\Exceptions\\UrlGenerationException(code: 0): Missing required parameter for [Route: users.edit] [URI: users/{provider}/edit] [Missing parameter: provider]. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Exceptions\\UrlGenerationException.php:35)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUrlGenerator.php(90): Illuminate\\Routing\\Exceptions\\UrlGenerationException::forMissingParameters(Object(Illuminate\\Routing\\Route), Array)
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php(500): Illuminate\\Routing\\RouteUrlGenerator->to(Object(Illuminate\\Routing\\Route), Array, true)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php(469): Illuminate\\Routing\\UrlGenerator->toRoute(Object(Illuminate\\Routing\\Route), Array, true)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('users.edit', Array, true)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\framework\\views\\80eba28b5a2c6b33e851dc3576838aad.php(273): route('users.edit', Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\PROJECTS\\\\Max...')
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\PROJECTS\\\\Max...', Array)
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\AdminMiddleware.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#71 {main}
"} 
[2025-08-04 12:04:04] local.INFO: Device time stored in session {"device_time":"2025-08-04 17:34:04 +0630","user_id":1} 
[2025-08-04 12:04:06] local.ERROR: Missing required parameter for [Route: users.edit] [URI: users/{provider}/edit] [Missing parameter: provider]. {"view":{"view":"C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\resources\\views\\scripts\\index-all.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-855638343 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#678</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-855638343\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-545957173 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"11 characters\">All scripts</span>\"
</pre><script>Sfdump(\"sf-dump-545957173\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","providers":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#714</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#717</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:27</span> [ &#8230;27]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:27</span> [ &#8230;27]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:23</span> [ &#8230;23]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#718</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:27</span> [ &#8230;27]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:27</span> [ &#8230;27]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:23</span> [ &#8230;23]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","clinic_names":"<pre class=sf-dump id=sf-dump-499958641 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\Collection</span> {<a class=sf-dump-ref>#716</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Aashish Clinic</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">Search in</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-499958641\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","medications":"<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#720</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Medication
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Medication</span></span> {<a class=sf-dump-ref>#723</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:9</span> [ &#8230;9]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">medications</span>\"
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Medication
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Medication</span></span> {<a class=sf-dump-ref>#724</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [ &#8230;14]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:9</span> [ &#8230;9]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">medications</span>\"
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Missing required parameter for [Route: users.edit] [URI: users/{provider}/edit] [Missing parameter: provider]. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Exceptions\\UrlGenerationException.php:35)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUrlGenerator.php(90): Illuminate\\Routing\\Exceptions\\UrlGenerationException::forMissingParameters(Object(Illuminate\\Routing\\Route), Array)
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php(500): Illuminate\\Routing\\RouteUrlGenerator->to(Object(Illuminate\\Routing\\Route), Array, true)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php(469): Illuminate\\Routing\\UrlGenerator->toRoute(Object(Illuminate\\Routing\\Route), Array, true)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('users.edit', Array, true)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\resources\\views\\scripts\\index-all.blade.php(272): route('users.edit', Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\PROJECTS\\\\Max...')
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\PROJECTS\\\\Max...', Array)
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\AdminMiddleware.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#71 {main}

[previous exception] [object] (Illuminate\\Routing\\Exceptions\\UrlGenerationException(code: 0): Missing required parameter for [Route: users.edit] [URI: users/{provider}/edit] [Missing parameter: provider]. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Exceptions\\UrlGenerationException.php:35)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUrlGenerator.php(90): Illuminate\\Routing\\Exceptions\\UrlGenerationException::forMissingParameters(Object(Illuminate\\Routing\\Route), Array)
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php(500): Illuminate\\Routing\\RouteUrlGenerator->to(Object(Illuminate\\Routing\\Route), Array, true)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php(469): Illuminate\\Routing\\UrlGenerator->toRoute(Object(Illuminate\\Routing\\Route), Array, true)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('users.edit', Array, true)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\framework\\views\\80eba28b5a2c6b33e851dc3576838aad.php(273): route('users.edit', Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\PROJECTS\\\\Max...')
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\PROJECTS\\\\Max...', Array)
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\AdminMiddleware.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#71 {main}
"} 
[2025-08-05 03:59:44] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 03:59:44] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-05 03:59:50] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-05 03:59:55] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-05 04:47:27] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 04:47:55] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:17:54 +0630","user_id":1} 
[2025-08-05 04:48:03] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:18:02 +0630","user_id":1} 
[2025-08-05 04:49:16] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:19:16 +0630","user_id":1} 
[2025-08-05 04:49:18] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:19:18 +0630","user_id":1} 
[2025-08-05 04:49:41] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:19:41 +0630","user_id":1} 
[2025-08-05 04:58:17] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:28:16 +0630","user_id":1} 
[2025-08-05 04:58:20] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:28:19 +0630","user_id":1} 
[2025-08-05 05:00:12] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:30:11 +0630","user_id":1} 
[2025-08-05 05:00:14] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:30:14 +0630","user_id":1} 
[2025-08-05 05:12:41] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 05:12:55] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 05:13:05] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 05:16:18] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:46:18 +0630","user_id":1} 
[2025-08-05 05:16:20] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:46:20 +0630","user_id":1} 
[2025-08-05 05:21:31] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:51:30 +0630","user_id":1} 
[2025-08-05 05:21:35] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:51:35 +0630","user_id":1} 
[2025-08-05 05:24:03] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:54:03 +0630","user_id":1} 
[2025-08-05 05:24:11] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:54:11 +0630","user_id":1} 
[2025-08-05 05:24:13] local.INFO: Device time stored in session {"device_time":"2025-08-05 10:54:13 +0630","user_id":1} 
[2025-08-05 05:45:11] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 05:46:48] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 05:53:36] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:23:36 +0630","user_id":1} 
[2025-08-05 06:02:06] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:32:06 +0630","user_id":1} 
[2025-08-05 06:02:16] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 06:02:28] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:32:28 +0630","user_id":1} 
[2025-08-05 06:04:18] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 06:06:24] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:36:24 +0630","user_id":1} 
[2025-08-05 06:10:57] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:40:57 +0630","user_id":1} 
[2025-08-05 06:11:17] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:41:16 +0630","user_id":1} 
[2025-08-05 06:11:20] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:41:20 +0630","user_id":1} 
[2025-08-05 06:11:29] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:41:28 +0630","user_id":1} 
[2025-08-05 06:11:32] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:41:32 +0630","user_id":1} 
[2025-08-05 06:18:51] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:48:51 +0630","user_id":3} 
[2025-08-05 06:18:58] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:48:57 +0630","user_id":3} 
[2025-08-05 06:19:00] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:49:00 +0630","user_id":3} 
[2025-08-05 06:19:02] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:49:01 +0630","user_id":3} 
[2025-08-05 06:19:05] local.INFO: Device time stored in session {"device_time":"2025-08-05 11:49:05 +0630","user_id":3} 
[2025-08-05 06:44:56] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 06:45:11] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 06:50:26] local.INFO: Device time stored in session {"device_time":"2025-08-05 12:20:26 +0630","user_id":1} 
[2025-08-05 06:51:19] local.INFO: Device time stored in session {"device_time":"2025-08-05 12:21:19 +0630","user_id":1} 
[2025-08-05 06:52:49] local.INFO: Device time stored in session {"device_time":"2025-08-05 12:22:48 +0630","user_id":1} 
[2025-08-05 06:53:11] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 06:53:23] local.INFO: Device time stored in session {"device_time":"2025-08-05 12:23:23 +0630","user_id":1} 
[2025-08-05 06:56:02] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 06:57:44] local.INFO: Device time stored in session {"device_time":"2025-08-05 12:27:43 +0630","user_id":1} 
[2025-08-05 06:57:58] local.INFO: Device time stored in session {"device_time":"2025-08-05 12:27:58 +0630","user_id":3} 
[2025-08-05 06:58:04] local.INFO: Device time stored in session {"device_time":"2025-08-05 12:28:03 +0630","user_id":3} 
[2025-08-05 06:58:11] local.INFO: Device time stored in session {"device_time":"2025-08-05 12:28:11 +0630","user_id":3} 
[2025-08-05 06:59:07] local.INFO: Device time stored in session {"device_time":"2025-08-05 12:29:07 +0630","user_id":1} 
[2025-08-05 07:02:01] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 07:02:06] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 07:07:17] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 08:43:28] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-05 08:46:36] local.INFO: Device time stored in session {"device_time":"2025-08-05 14:16:36 +0630","user_id":1} 
[2025-08-05 08:46:40] local.INFO: Device time stored in session {"device_time":"2025-08-05 14:16:40 +0630","user_id":1} 
[2025-08-05 08:47:10] local.INFO: Device time stored in session {"device_time":"2025-08-05 14:17:10 +0630","user_id":1} 
[2025-08-05 08:47:51] local.INFO: Device time stored in session {"device_time":"2025-08-05 14:17:50 +0630","user_id":1} 
[2025-08-05 08:47:55] local.INFO: Device time stored in session {"device_time":"2025-08-05 14:17:55 +0630","user_id":1} 
[2025-08-05 08:51:13] local.INFO: Device time stored in session {"device_time":"2025-08-05 14:21:13 +0630","user_id":1} 
[2025-08-05 08:58:36] local.INFO: Device time stored in session {"device_time":"2025-08-05 14:28:36 +0630","user_id":1} 
[2025-08-05 08:58:40] local.INFO: Device time stored in session {"device_time":"2025-08-05 14:28:39 +0630","user_id":1} 
[2025-08-05 10:24:57] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:54:57 +0630","user_id":1} 
[2025-08-05 10:25:05] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:55:05 +0630","user_id":1} 
[2025-08-05 10:25:06] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:55:06 +0630","user_id":1} 
[2025-08-05 10:25:20] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:55:20 +0630","user_id":1} 
[2025-08-05 10:25:24] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:55:23 +0630","user_id":1} 
[2025-08-05 10:25:55] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:55:54 +0630","user_id":1} 
[2025-08-05 10:27:32] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:57:32 +0630","user_id":3} 
[2025-08-05 10:27:34] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:57:34 +0630","user_id":3} 
[2025-08-05 10:27:36] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:57:36 +0630","user_id":3} 
[2025-08-05 10:27:43] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:57:43 +0630","user_id":3} 
[2025-08-05 10:27:43] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-08-05 15:57:43 +0630","error":"Trailing data"} 
[2025-08-05 10:27:43] local.INFO: Using device time from session for signed_at {"timestamp":"2025-08-05 15:57:43 +0630"} 
[2025-08-05 10:27:43] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":9,"signed_at_db":"2025-08-05 15:57:43","formatted_signed_at":"08/05/2025 03:57 PM"} 
[2025-08-05 10:27:43] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":10,"signed_at_db":"2025-08-05 15:57:43","formatted_signed_at":"08/05/2025 03:57 PM"} 
[2025-08-05 10:27:43] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":11,"signed_at_db":"2025-08-05 15:57:43","formatted_signed_at":"08/05/2025 03:57 PM"} 
[2025-08-05 10:27:43] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":12,"signed_at_db":"2025-08-05 15:57:43","formatted_signed_at":"08/05/2025 03:57 PM"} 
[2025-08-05 10:27:44] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"Aashish Rawat","count":4} 
[2025-08-05 10:27:44] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:57:44 +0630","user_id":3} 
[2025-08-05 10:27:54] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:57:54 +0630","user_id":1} 
[2025-08-05 10:28:42] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:58:42 +0630","user_id":1} 
[2025-08-05 10:28:47] local.INFO: Device time stored in session {"device_time":"2025-08-05 15:58:47 +0630","user_id":1} 
[2025-08-05 10:30:04] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:00:04 +0630","user_id":1} 
[2025-08-05 10:30:06] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:00:06 +0630","user_id":1} 
[2025-08-05 10:30:26] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:00:25 +0630","user_id":1} 
[2025-08-05 10:30:29] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:00:28 +0630","user_id":1} 
[2025-08-05 10:30:37] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:00:36 +0630","user_id":1} 
[2025-08-05 10:30:39] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:00:39 +0630","user_id":1} 
[2025-08-05 10:30:43] local.INFO: sendFaxAll request parameters {"all_params":{"_token":"l82AFvaqd0xc0WgyYTqf2LYjCq2g777JAUk7wdkL","status":"Pending Approval","changed_status":"Sent","displayed_ids":["9","10"]},"provider_id":null,"has_provider_id":false,"medication_id":null,"signed_date":null,"displayed_ids":["9","10"]} 
[2025-08-05 10:30:43] local.WARNING: No provider filter applied {"has_provider_id":false,"provider_id_value":null} 
[2025-08-05 10:30:43] local.INFO: Final query before execution {"sql":"select * from `import_files` where `status` = ?","bindings":["Pending Approval"]} 
[2025-08-05 10:30:43] local.INFO: Filtering displayed_ids {"original_count":2,"filtered_count":2,"original_ids":["9","10"],"filtered_ids":["9","10"]} 
[2025-08-05 10:30:43] local.INFO: Using filtered displayed_ids {"count":2,"results_count":2} 
[2025-08-05 10:30:43] local.INFO: Sending IDs to SendFilesToFaxJob {"import_file_fax":[],"import_file_dispense":[9,10],"count":2} 
[2025-08-05 10:30:43] local.WARNING: No import file IDs to process  
[2025-08-05 10:30:43] local.INFO: SendDispenseJob dispatched successfully in chunks {"user_id":1} 
[2025-08-05 10:30:44] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:00:44 +0630","user_id":1} 
[2025-08-05 10:30:45] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:00:45 +0630","user_id":1} 
[2025-08-05 10:30:59] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:00:59 +0630","user_id":1} 
[2025-08-05 10:31:00] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:01:00 +0630","user_id":1} 
[2025-08-05 10:31:01] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:01:01 +0630","user_id":1} 
[2025-08-05 10:31:02] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:01:02 +0630","user_id":1} 
[2025-08-05 10:31:03] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 9  
[2025-08-05 10:31:19] local.INFO: DispensePro order sent successfully for ImportFile ID: 9  
[2025-08-05 10:31:19] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 10  
[2025-08-05 10:31:36] local.INFO: DispensePro order sent successfully for ImportFile ID: 10  
[2025-08-05 10:32:25] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:02:25 +0630","user_id":1} 
[2025-08-05 10:32:47] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:02:47 +0630","user_id":1} 
[2025-08-05 10:33:56] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:03:56 +0630","user_id":1} 
[2025-08-05 10:33:58] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:03:58 +0630","user_id":1} 
[2025-08-05 10:33:59] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:03:59 +0630","user_id":1} 
[2025-08-05 10:34:07] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:04:07 +0630","user_id":1} 
[2025-08-05 10:34:10] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:04:10 +0630","user_id":1} 
[2025-08-05 10:34:21] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:04:20 +0630","user_id":1} 
[2025-08-05 10:34:24] local.INFO: Processing Staff Excel data {"total_rows":18,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"Aashish Rawat"} 
[2025-08-05 10:34:25] local.INFO: Staff bulk import completed {"import_id":2,"processed_rows":18,"skipped_rows":0,"provider_id":"3","provider_name":"Aashish"} 
[2025-08-05 10:34:25] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:04:25 +0630","user_id":1} 
[2025-08-05 10:34:34] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:04:34 +0630","user_id":3} 
[2025-08-05 10:34:51] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:04:51 +0630","user_id":3} 
[2025-08-05 10:34:51] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-08-05 16:04:51 +0630","error":"Trailing data"} 
[2025-08-05 10:34:51] local.INFO: Using device time from session for signed_at {"timestamp":"2025-08-05 16:04:51 +0630"} 
[2025-08-05 10:34:51] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":19,"signed_at_db":"2025-08-05 16:04:51","formatted_signed_at":"08/05/2025 04:04 PM"} 
[2025-08-05 10:34:51] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":20,"signed_at_db":"2025-08-05 16:04:51","formatted_signed_at":"08/05/2025 04:04 PM"} 
[2025-08-05 10:34:52] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":34,"signed_at_db":"2025-08-05 16:04:51","formatted_signed_at":"08/05/2025 04:04 PM"} 
[2025-08-05 10:34:52] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":35,"signed_at_db":"2025-08-05 16:04:51","formatted_signed_at":"08/05/2025 04:04 PM"} 
[2025-08-05 10:34:52] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":36,"signed_at_db":"2025-08-05 16:04:51","formatted_signed_at":"08/05/2025 04:04 PM"} 
[2025-08-05 10:34:52] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"Aashish Rawat","count":5} 
[2025-08-05 10:34:53] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:04:52 +0630","user_id":3} 
[2025-08-05 10:34:56] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:04:55 +0630","user_id":3} 
[2025-08-05 10:34:57] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:04:57 +0630","user_id":1} 
[2025-08-05 10:34:59] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:04:58 +0630","user_id":1} 
[2025-08-05 10:35:03] local.INFO: sendFaxAll request parameters {"all_params":{"_token":"l82AFvaqd0xc0WgyYTqf2LYjCq2g777JAUk7wdkL","status":"Pending Approval","changed_status":"Sent","displayed_ids":["34","35","36","19","20"]},"provider_id":null,"has_provider_id":false,"medication_id":null,"signed_date":null,"displayed_ids":["34","35","36","19","20"]} 
[2025-08-05 10:35:03] local.WARNING: No provider filter applied {"has_provider_id":false,"provider_id_value":null} 
[2025-08-05 10:35:03] local.INFO: Final query before execution {"sql":"select * from `import_files` where `status` = ?","bindings":["Pending Approval"]} 
[2025-08-05 10:35:03] local.INFO: Filtering displayed_ids {"original_count":5,"filtered_count":5,"original_ids":["34","35","36","19","20"],"filtered_ids":["34","35","36","19","20"]} 
[2025-08-05 10:35:03] local.INFO: Using filtered displayed_ids {"count":5,"results_count":5} 
[2025-08-05 10:35:03] local.INFO: Sending IDs to SendFilesToFaxJob {"import_file_fax":[],"import_file_dispense":[19,20,34,35,36],"count":5} 
[2025-08-05 10:35:03] local.WARNING: No import file IDs to process  
[2025-08-05 10:35:03] local.INFO: SendDispenseJob dispatched successfully in chunks {"user_id":1} 
[2025-08-05 10:35:03] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 19  
[2025-08-05 10:35:04] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:05:04 +0630","user_id":1} 
[2025-08-05 10:35:08] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:05:07 +0630","user_id":1} 
[2025-08-05 10:35:15] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:05:14 +0630","user_id":1} 
[2025-08-05 10:35:19] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:05:19 +0630","user_id":1} 
[2025-08-05 10:35:20] local.INFO: DispensePro order sent successfully for ImportFile ID: 19  
[2025-08-05 10:35:20] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 20  
[2025-08-05 10:35:32] local.INFO: DispensePro order sent successfully for ImportFile ID: 20  
[2025-08-05 10:35:32] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 34  
[2025-08-05 10:35:44] local.INFO: DispensePro order sent successfully for ImportFile ID: 34  
[2025-08-05 10:35:44] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 35  
[2025-08-05 10:36:01] local.INFO: DispensePro order sent successfully for ImportFile ID: 35  
[2025-08-05 10:36:01] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 36  
[2025-08-05 10:36:03] local.ERROR: The process "C:\xampp\php\php.exe artisan queue:work --once --name=default --queue=default --backoff=0 --memory=128 --sleep=3 --tries=1" exceeded the timeout of 60 seconds. {"exception":"[object] (Symfony\\Component\\Process\\Exception\\ProcessTimedOutException(code: 0): The process \"C:\\xampp\\php\\php.exe artisan queue:work --once --name=default --queue=default --backoff=0 --memory=128 --sleep=3 --tries=1\" exceeded the timeout of 60 seconds. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\process\\Process.php:1156)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\process\\Process.php(423): Symfony\\Component\\Process\\Process->checkTimeout()
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\process\\Process.php(251): Symfony\\Component\\Process\\Process->wait()
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Listener.php(179): Symfony\\Component\\Process\\Process->run(Object(Closure))
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Listener.php(90): Illuminate\\Queue\\Listener->runProcess(Object(Symfony\\Component\\Process\\Process), '128')
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\ListenCommand.php(74): Illuminate\\Queue\\Listener->listen(NULL, 'default', Object(Illuminate\\Queue\\ListenerOptions))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\ListenCommand->handle()
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\ListenCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 {main}
"} 
[2025-08-05 10:36:18] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:06:18 +0630","user_id":1} 
[2025-08-05 10:36:27] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:06:27 +0630","user_id":1} 
[2025-08-05 10:36:51] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:06:51 +0630","user_id":1} 
[2025-08-05 10:37:59] local.INFO: Admin or operator void request {"user_id":1,"status":"Sent","requested_order_ids":["UCV55039","8NS63473","EG232635","VBW32580"],"filtered_order_ids":["EG232635","VBW32580","UCV55039","8NS63473"],"count":4} 
[2025-08-05 10:37:59] local.INFO: Sending IDs to VoidScriptJob {"order_ids":["EG232635","VBW32580","UCV55039","8NS63473"],"count":4,"user_role":"administrator"} 
[2025-08-05 10:37:59] local.INFO: Void job has been queued in chunks. {"order_ids":["EG232635","VBW32580","UCV55039","8NS63473"],"count":4} 
[2025-08-05 10:38:03] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:08:03 +0630","user_id":1} 
[2025-08-05 10:38:05] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:08:04 +0630","user_id":1} 
[2025-08-05 10:38:07] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:08:07 +0630","user_id":1} 
[2025-08-05 10:38:10] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:08:10 +0630","user_id":1} 
[2025-08-05 10:38:28] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:08:27 +0630","user_id":1} 
[2025-08-05 10:38:29] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:08:28 +0630","user_id":1} 
[2025-08-05 10:38:34] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:08:33 +0630","user_id":3} 
[2025-08-05 10:38:39] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:08:39 +0630","user_id":3} 
[2025-08-05 10:38:39] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-08-05 16:08:39 +0630","error":"Trailing data"} 
[2025-08-05 10:38:39] local.INFO: Using device time from session for signed_at {"timestamp":"2025-08-05 16:08:39 +0630"} 
[2025-08-05 10:38:39] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":21,"signed_at_db":"2025-08-05 16:08:39","formatted_signed_at":"08/05/2025 04:08 PM"} 
[2025-08-05 10:38:39] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":22,"signed_at_db":"2025-08-05 16:08:39","formatted_signed_at":"08/05/2025 04:08 PM"} 
[2025-08-05 10:38:39] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"Aashish Rawat","count":2} 
[2025-08-05 10:38:40] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:08:40 +0630","user_id":3} 
[2025-08-05 10:38:43] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:08:43 +0630","user_id":1} 
[2025-08-05 10:38:46] local.INFO: sendFaxAll request parameters {"all_params":{"_token":"l82AFvaqd0xc0WgyYTqf2LYjCq2g777JAUk7wdkL","status":"Pending Approval","changed_status":"Sent","displayed_ids":["21","22"]},"provider_id":null,"has_provider_id":false,"medication_id":null,"signed_date":null,"displayed_ids":["21","22"]} 
[2025-08-05 10:38:46] local.WARNING: No provider filter applied {"has_provider_id":false,"provider_id_value":null} 
[2025-08-05 10:38:46] local.INFO: Final query before execution {"sql":"select * from `import_files` where `status` = ?","bindings":["Pending Approval"]} 
[2025-08-05 10:38:46] local.INFO: Filtering displayed_ids {"original_count":2,"filtered_count":2,"original_ids":["21","22"],"filtered_ids":["21","22"]} 
[2025-08-05 10:38:46] local.INFO: Using filtered displayed_ids {"count":2,"results_count":2} 
[2025-08-05 10:38:46] local.INFO: Sending IDs to SendFilesToFaxJob {"import_file_fax":[],"import_file_dispense":[21,22],"count":2} 
[2025-08-05 10:38:46] local.WARNING: No import file IDs to process  
[2025-08-05 10:38:47] local.INFO: SendDispenseJob dispatched successfully in chunks {"user_id":1} 
[2025-08-05 10:38:47] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:08:47 +0630","user_id":1} 
[2025-08-05 10:38:50] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:08:50 +0630","user_id":1} 
[2025-08-05 10:39:07] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:09:07 +0630","user_id":1} 
[2025-08-05 10:40:19] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:10:18 +0630","user_id":1} 
[2025-08-05 10:40:24] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:10:23 +0630","user_id":1} 
[2025-08-05 10:40:27] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:10:27 +0630","user_id":1} 
[2025-08-05 10:40:49] local.ERROR: App\Jobs\SendDispenseJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\SendDispenseJob has been attempted too many times. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-08-05 10:41:21] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 21  
[2025-08-05 10:41:37] local.INFO: DispensePro order sent successfully for ImportFile ID: 21  
[2025-08-05 10:41:37] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 22  
[2025-08-05 10:41:54] local.INFO: DispensePro order sent successfully for ImportFile ID: 22  
[2025-08-05 10:42:01] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:12:01 +0630","user_id":1} 
[2025-08-05 10:42:20] local.ERROR: App\Jobs\VoidScriptJob has been attempted too many times. {"exception":"[object] (Illuminate\\Queue\\MaxAttemptsExceededException(code: 0): App\\Jobs\\VoidScriptJob has been attempted too many times. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\MaxAttemptsExceededException.php:24)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(428): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(137): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-08-05 10:43:34] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:13:33 +0630","user_id":1} 
[2025-08-05 10:43:38] local.INFO: Admin or operator void request {"user_id":1,"status":"Sent","requested_order_ids":["SK129893","7OW15930"],"filtered_order_ids":["SK129893","7OW15930"],"count":2} 
[2025-08-05 10:43:38] local.INFO: Sending IDs to VoidScriptJob {"order_ids":["SK129893","7OW15930"],"count":2,"user_role":"administrator"} 
[2025-08-05 10:43:38] local.INFO: Void job has been queued in chunks. {"order_ids":["SK129893","7OW15930"],"count":2} 
[2025-08-05 10:44:12] local.INFO: VoidScriptJob results [{"orderId":"SK129893","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"dKMlxyB2x8","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"VALIDLASTNAME","firstName":"VALIDFIRSTNAME","middleName":"","address":"458 VALID ADDRESS ST","address2":"","city":"VALID CITY","region":"Alabama","postal":"12345","countryCode":"US","homePhone":"","cellPhone":"999462160","workPhone":"","email":"","dob":"11/16/2005","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}},{"orderId":"7OW15930","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"BIACvCjsBE","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"VALIDLASTNAME","firstName":"VALIDFIRSTNAME","middleName":"","address":"459 VALID ADDRESS ST","address2":"","city":"VALID CITY","region":"Alabama","postal":"12345","countryCode":"US","homePhone":"","cellPhone":"999462160","workPhone":"","email":"","dob":"11/16/2006","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}}] 
[2025-08-05 10:44:12] local.INFO: VoidScriptJob completed {"voided_order_ids":["SK129893","7OW15930"],"failed_order_ids":[],"results":[{"orderId":"SK129893","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"dKMlxyB2x8","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"VALIDLASTNAME","firstName":"VALIDFIRSTNAME","middleName":"","address":"458 VALID ADDRESS ST","address2":"","city":"VALID CITY","region":"Alabama","postal":"12345","countryCode":"US","homePhone":"","cellPhone":"999462160","workPhone":"","email":"","dob":"11/16/2005","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}},{"orderId":"7OW15930","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"BIACvCjsBE","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"VALIDLASTNAME","firstName":"VALIDFIRSTNAME","middleName":"","address":"459 VALID ADDRESS ST","address2":"","city":"VALID CITY","region":"Alabama","postal":"12345","countryCode":"US","homePhone":"","cellPhone":"999462160","workPhone":"","email":"","dob":"11/16/2006","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}}],"message":"2 script(s) voided, 0 failed.","user_id":1} 
[2025-08-05 10:53:17] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:17 +0630","user_id":1} 
[2025-08-05 10:53:18] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:18 +0630","user_id":1} 
[2025-08-05 10:53:22] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:22 +0630","user_id":3} 
[2025-08-05 10:53:27] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:27 +0630","user_id":3} 
[2025-08-05 10:53:27] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-08-05 16:23:27 +0630","error":"Trailing data"} 
[2025-08-05 10:53:27] local.INFO: Using device time from session for signed_at {"timestamp":"2025-08-05 16:23:27 +0630"} 
[2025-08-05 10:53:27] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":23,"signed_at_db":"2025-08-05 16:23:27","formatted_signed_at":"08/05/2025 04:23 PM"} 
[2025-08-05 10:53:27] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":24,"signed_at_db":"2025-08-05 16:23:27","formatted_signed_at":"08/05/2025 04:23 PM"} 
[2025-08-05 10:53:27] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":25,"signed_at_db":"2025-08-05 16:23:27","formatted_signed_at":"08/05/2025 04:23 PM"} 
[2025-08-05 10:53:27] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":26,"signed_at_db":"2025-08-05 16:23:27","formatted_signed_at":"08/05/2025 04:23 PM"} 
[2025-08-05 10:53:28] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"Aashish Rawat","count":4} 
[2025-08-05 10:53:28] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:28 +0630","user_id":3} 
[2025-08-05 10:53:31] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:31 +0630","user_id":1} 
[2025-08-05 10:53:35] local.INFO: sendFaxAll request parameters {"all_params":{"_token":"l82AFvaqd0xc0WgyYTqf2LYjCq2g777JAUk7wdkL","status":"Pending Approval","changed_status":"Sent","displayed_ids":["23"]},"provider_id":null,"has_provider_id":false,"medication_id":null,"signed_date":null,"displayed_ids":["23"]} 
[2025-08-05 10:53:35] local.WARNING: No provider filter applied {"has_provider_id":false,"provider_id_value":null} 
[2025-08-05 10:53:35] local.INFO: Final query before execution {"sql":"select * from `import_files` where `status` = ?","bindings":["Pending Approval"]} 
[2025-08-05 10:53:35] local.INFO: Filtering displayed_ids {"original_count":1,"filtered_count":1,"original_ids":["23"],"filtered_ids":["23"]} 
[2025-08-05 10:53:35] local.INFO: Using filtered displayed_ids {"count":1,"results_count":1} 
[2025-08-05 10:53:35] local.INFO: Sending IDs to SendFilesToFaxJob {"import_file_fax":[],"import_file_dispense":[23],"count":1} 
[2025-08-05 10:53:35] local.WARNING: No import file IDs to process  
[2025-08-05 10:53:35] local.INFO: SendDispenseJob dispatched successfully in chunks {"user_id":1} 
[2025-08-05 10:53:36] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 23  
[2025-08-05 10:53:36] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:35 +0630","user_id":1} 
[2025-08-05 10:53:40] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:40 +0630","user_id":3} 
[2025-08-05 10:53:43] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:43 +0630","user_id":3} 
[2025-08-05 10:53:44] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:43 +0630","user_id":3} 
[2025-08-05 10:53:45] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:44 +0630","user_id":3} 
[2025-08-05 10:53:45] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:45 +0630","user_id":3} 
[2025-08-05 10:53:47] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:46 +0630","user_id":3} 
[2025-08-05 10:53:47] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:47 +0630","user_id":3} 
[2025-08-05 10:53:51] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:50 +0630","user_id":1} 
[2025-08-05 10:53:52] local.INFO: DispensePro order sent successfully for ImportFile ID: 23  
[2025-08-05 10:53:53] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:52 +0630","user_id":1} 
[2025-08-05 10:53:55] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:54 +0630","user_id":1} 
[2025-08-05 10:53:57] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:56 +0630","user_id":1} 
[2025-08-05 10:53:58] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:23:58 +0630","user_id":1} 
[2025-08-05 10:54:07] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:24:06 +0630","user_id":1} 
[2025-08-05 10:55:18] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:25:17 +0630","user_id":3} 
[2025-08-05 10:55:20] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:25:19 +0630","user_id":1} 
[2025-08-05 10:55:23] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:25:22 +0630","user_id":1} 
[2025-08-05 10:55:44] local.INFO: DispensePro order sent successfully for ImportFile ID: 24  
[2025-08-05 10:55:45] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:25:45 +0630","user_id":1} 
[2025-08-05 10:55:49] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:25:49 +0630","user_id":1} 
[2025-08-05 10:55:57] local.INFO: Admin or operator void request {"user_id":1,"status":"Sent","requested_order_ids":["AZE15793","QU378349"],"filtered_order_ids":["AZE15793","QU378349"],"count":2} 
[2025-08-05 10:55:57] local.INFO: Sending IDs to VoidScriptJob {"order_ids":["AZE15793","QU378349"],"count":2,"user_role":"administrator"} 
[2025-08-05 10:55:57] local.INFO: Void job has been queued in chunks. {"order_ids":["AZE15793","QU378349"],"count":2} 
[2025-08-05 10:56:01] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:26:00 +0630","user_id":1} 
[2025-08-05 10:56:24] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:26:24 +0630","user_id":1} 
[2025-08-05 10:56:30] local.INFO: VoidScriptJob results [{"orderId":"AZE15793","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"UoRRG3MHM8","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"VALIDLASTNAME","firstName":"VALIDFIRSTNAME","middleName":"","address":"460 VALID ADDRESS ST","address2":"","city":"VALID CITY","region":"Alabama","postal":"12345-6789","countryCode":"US","homePhone":"","cellPhone":"999462160","workPhone":"","email":"","dob":"11/16/2007","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}},{"orderId":"QU378349","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"ruhoDocpJY","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"VALIDLASTNAME","firstName":"VALIDFIRSTNAME","middleName":"","address":"461 VALID ADDRESS ST","address2":"","city":"VALID CITY","region":"Alabama","postal":"12345","countryCode":"US","homePhone":"","cellPhone":"999462160","workPhone":"","email":"","dob":"11/16/2008","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}}] 
[2025-08-05 10:56:30] local.INFO: VoidScriptJob completed {"voided_order_ids":["AZE15793","QU378349"],"failed_order_ids":[],"results":[{"orderId":"AZE15793","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"UoRRG3MHM8","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"VALIDLASTNAME","firstName":"VALIDFIRSTNAME","middleName":"","address":"460 VALID ADDRESS ST","address2":"","city":"VALID CITY","region":"Alabama","postal":"12345-6789","countryCode":"US","homePhone":"","cellPhone":"999462160","workPhone":"","email":"","dob":"11/16/2007","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}},{"orderId":"QU378349","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"ruhoDocpJY","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"VALIDLASTNAME","firstName":"VALIDFIRSTNAME","middleName":"","address":"461 VALID ADDRESS ST","address2":"","city":"VALID CITY","region":"Alabama","postal":"12345","countryCode":"US","homePhone":"","cellPhone":"999462160","workPhone":"","email":"","dob":"11/16/2008","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}}],"message":"2 script(s) voided, 0 failed.","user_id":1} 
[2025-08-05 10:56:33] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:26:32 +0630","user_id":1} 
[2025-08-05 10:56:34] local.INFO: sendFaxAll request parameters {"all_params":{"_token":"l82AFvaqd0xc0WgyYTqf2LYjCq2g777JAUk7wdkL","status":"Pending Approval","changed_status":"Sent","displayed_ids":["25","26"]},"provider_id":null,"has_provider_id":false,"medication_id":null,"signed_date":null,"displayed_ids":["25","26"]} 
[2025-08-05 10:56:34] local.WARNING: No provider filter applied {"has_provider_id":false,"provider_id_value":null} 
[2025-08-05 10:56:34] local.INFO: Final query before execution {"sql":"select * from `import_files` where `status` = ?","bindings":["Pending Approval"]} 
[2025-08-05 10:56:34] local.INFO: Filtering displayed_ids {"original_count":2,"filtered_count":2,"original_ids":["25","26"],"filtered_ids":["25","26"]} 
[2025-08-05 10:56:34] local.INFO: Using filtered displayed_ids {"count":2,"results_count":2} 
[2025-08-05 10:56:34] local.INFO: Sending IDs to SendFilesToFaxJob {"import_file_fax":[],"import_file_dispense":[25,26],"count":2} 
[2025-08-05 10:56:34] local.WARNING: No import file IDs to process  
[2025-08-05 10:56:34] local.INFO: SendDispenseJob dispatched successfully in chunks {"user_id":1} 
[2025-08-05 10:56:36] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:26:35 +0630","user_id":1} 
[2025-08-05 10:56:36] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 25  
[2025-08-05 10:56:37] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:26:37 +0630","user_id":1} 
[2025-08-05 10:56:44] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:26:43 +0630","user_id":1} 
[2025-08-05 10:56:45] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:26:45 +0630","user_id":1} 
[2025-08-05 10:56:48] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:26:47 +0630","user_id":1} 
[2025-08-05 10:56:49] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:26:49 +0630","user_id":1} 
[2025-08-05 10:56:54] local.INFO: DispensePro order sent successfully for ImportFile ID: 25  
[2025-08-05 10:56:54] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 26  
[2025-08-05 10:57:03] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:27:02 +0630","user_id":1} 
[2025-08-05 10:57:06] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:27:05 +0630","user_id":1} 
[2025-08-05 10:57:10] local.INFO: DispensePro order sent successfully for ImportFile ID: 26  
[2025-08-05 10:57:14] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:27:14 +0630","user_id":1} 
[2025-08-05 10:57:16] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:27:15 +0630","user_id":1} 
[2025-08-05 10:59:40] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:29:40 +0630","user_id":1} 
[2025-08-05 11:00:02] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:30:02 +0630","user_id":1} 
[2025-08-05 11:00:12] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:30:12 +0630","user_id":1} 
[2025-08-05 11:00:20] local.INFO: Admin or operator void request {"user_id":1,"status":"Sent","requested_order_ids":["RIW71946","LSI19613"],"filtered_order_ids":["RIW71946","LSI19613"],"count":2} 
[2025-08-05 11:00:20] local.INFO: Sending IDs to VoidScriptJob {"order_ids":["RIW71946","LSI19613"],"count":2,"user_role":"administrator"} 
[2025-08-05 11:00:20] local.INFO: Void job has been queued in chunks. {"order_ids":["RIW71946","LSI19613"],"count":2} 
[2025-08-05 11:00:24] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:30:24 +0630","user_id":1} 
[2025-08-05 11:00:55] local.INFO: VoidScriptJob results [{"orderId":"RIW71946","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"TIeYJCiLoQ","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"VALIDLASTNAME","firstName":"VALIDFIRSTNAME","middleName":"","address":"462 VALID ADDRESS ST","address2":"","city":"VALID CITY","region":"Alabama","postal":"12345","countryCode":"US","homePhone":"","cellPhone":"999462160","workPhone":"","email":"","dob":"11/16/2009","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}},{"orderId":"LSI19613","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"pGHZkNcSE3","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"VALIDLASTNAME","firstName":"VALIDFIRSTNAME","middleName":"","address":"463 VALID ADDRESS ST","address2":"","city":"VALID CITY","region":"Alabama","postal":"12345-6789","countryCode":"US","homePhone":"","cellPhone":"999462160","workPhone":"","email":"","dob":"11/16/2010","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}}] 
[2025-08-05 11:00:55] local.INFO: VoidScriptJob completed {"voided_order_ids":["RIW71946","LSI19613"],"failed_order_ids":[],"results":[{"orderId":"RIW71946","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"TIeYJCiLoQ","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"VALIDLASTNAME","firstName":"VALIDFIRSTNAME","middleName":"","address":"462 VALID ADDRESS ST","address2":"","city":"VALID CITY","region":"Alabama","postal":"12345","countryCode":"US","homePhone":"","cellPhone":"999462160","workPhone":"","email":"","dob":"11/16/2009","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}},{"orderId":"LSI19613","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"pGHZkNcSE3","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"VALIDLASTNAME","firstName":"VALIDFIRSTNAME","middleName":"","address":"463 VALID ADDRESS ST","address2":"","city":"VALID CITY","region":"Alabama","postal":"12345-6789","countryCode":"US","homePhone":"","cellPhone":"999462160","workPhone":"","email":"","dob":"11/16/2010","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}}],"message":"2 script(s) voided, 0 failed.","user_id":1} 
[2025-08-05 11:03:02] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:33:01 +0630","user_id":1} 
[2025-08-05 11:03:05] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:33:05 +0630","user_id":3} 
[2025-08-05 11:03:07] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:33:07 +0630","user_id":3} 
[2025-08-05 11:03:08] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-08-05 16:33:07 +0630","error":"Trailing data"} 
[2025-08-05 11:03:08] local.INFO: Using device time from session for signed_at {"timestamp":"2025-08-05 16:33:07 +0630"} 
[2025-08-05 11:03:08] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":27,"signed_at_db":"2025-08-05 16:33:07","formatted_signed_at":"08/05/2025 04:33 PM"} 
[2025-08-05 11:03:08] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":28,"signed_at_db":"2025-08-05 16:33:07","formatted_signed_at":"08/05/2025 04:33 PM"} 
[2025-08-05 11:03:08] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":29,"signed_at_db":"2025-08-05 16:33:07","formatted_signed_at":"08/05/2025 04:33 PM"} 
[2025-08-05 11:03:08] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":30,"signed_at_db":"2025-08-05 16:33:07","formatted_signed_at":"08/05/2025 04:33 PM"} 
[2025-08-05 11:03:08] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":31,"signed_at_db":"2025-08-05 16:33:07","formatted_signed_at":"08/05/2025 04:33 PM"} 
[2025-08-05 11:03:08] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":32,"signed_at_db":"2025-08-05 16:33:07","formatted_signed_at":"08/05/2025 04:33 PM"} 
[2025-08-05 11:03:08] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":33,"signed_at_db":"2025-08-05 16:33:07","formatted_signed_at":"08/05/2025 04:33 PM"} 
[2025-08-05 11:03:08] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"Aashish Rawat","count":7} 
[2025-08-05 11:03:09] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:33:09 +0630","user_id":3} 
[2025-08-05 11:03:11] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:33:10 +0630","user_id":1} 
[2025-08-05 11:03:15] local.INFO: sendFaxAll request parameters {"all_params":{"_token":"l82AFvaqd0xc0WgyYTqf2LYjCq2g777JAUk7wdkL","status":"Pending Approval","changed_status":"Sent","displayed_ids":["27","28"]},"provider_id":null,"has_provider_id":false,"medication_id":null,"signed_date":null,"displayed_ids":["27","28"]} 
[2025-08-05 11:03:15] local.WARNING: No provider filter applied {"has_provider_id":false,"provider_id_value":null} 
[2025-08-05 11:03:15] local.INFO: Final query before execution {"sql":"select * from `import_files` where `status` = ?","bindings":["Pending Approval"]} 
[2025-08-05 11:03:15] local.INFO: Filtering displayed_ids {"original_count":2,"filtered_count":2,"original_ids":["27","28"],"filtered_ids":["27","28"]} 
[2025-08-05 11:03:15] local.INFO: Using filtered displayed_ids {"count":2,"results_count":2} 
[2025-08-05 11:03:15] local.INFO: Sending IDs to SendFilesToFaxJob {"import_file_fax":[],"import_file_dispense":[27,28],"count":2} 
[2025-08-05 11:03:15] local.WARNING: No import file IDs to process  
[2025-08-05 11:03:15] local.INFO: SendDispenseJob dispatched successfully in chunks {"user_id":1} 
[2025-08-05 11:03:17] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:33:16 +0630","user_id":1} 
[2025-08-05 11:03:17] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 27  
[2025-08-05 11:03:19] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:33:19 +0630","user_id":1} 
[2025-08-05 11:03:33] local.INFO: DispensePro order sent successfully for ImportFile ID: 27  
[2025-08-05 11:03:33] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 28  
[2025-08-05 11:03:34] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:33:33 +0630","user_id":1} 
[2025-08-05 11:03:35] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:33:35 +0630","user_id":1} 
[2025-08-05 11:03:37] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:33:37 +0630","user_id":1} 
[2025-08-05 11:03:44] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:33:44 +0630","user_id":1} 
[2025-08-05 11:03:50] local.INFO: DispensePro order sent successfully for ImportFile ID: 28  
[2025-08-05 11:06:33] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:36:32 +0630","user_id":1} 
[2025-08-05 11:07:14] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:37:14 +0630","user_id":1} 
[2025-08-05 11:08:03] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:38:02 +0630","user_id":1} 
[2025-08-05 11:09:51] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:39:50 +0630","user_id":1} 
[2025-08-05 11:10:47] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:40:46 +0630","user_id":1} 
[2025-08-05 11:11:21] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:41:20 +0630","user_id":1} 
[2025-08-05 11:11:35] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:41:34 +0630","user_id":1} 
[2025-08-05 11:12:19] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:42:19 +0630","user_id":1} 
[2025-08-05 11:12:49] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:42:49 +0630","user_id":1} 
[2025-08-05 11:14:34] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:44:33 +0630","user_id":1} 
[2025-08-05 11:14:48] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:44:47 +0630","user_id":1} 
[2025-08-05 11:15:29] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:45:29 +0630","user_id":1} 
[2025-08-05 11:15:57] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:45:57 +0630","user_id":1} 
[2025-08-05 11:16:11] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:46:10 +0630","user_id":1} 
[2025-08-05 11:17:07] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:47:06 +0630","user_id":1} 
[2025-08-05 11:17:49] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:47:48 +0630","user_id":1} 
[2025-08-05 11:19:43] local.INFO: Device time stored in session {"device_time":"2025-08-05 16:49:42 +0630","user_id":1} 

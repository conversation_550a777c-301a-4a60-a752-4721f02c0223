@php
    use App\Models\User;
@endphp
<div>
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('message') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    <form wire:submit.prevent="store">
        <x-layout.row>
            <div class="col">
                <x-card.body>
                    <x-layout.row>

                        <x-layout.col>
                            <x-form.input.text label="Name" labelRequired="1" model="practice.name" />
                        </x-layout.col>

                        <x-layout.col>
                            <x-form.input.text label="Address" labelRequired="1" model="practice.address" />
                        </x-layout.col>

                        <x-layout.col>
                            <x-form.input.text label="City" labelRequired="1" model="practice.city" />
                        </x-layout.col>

                        <x-layout.col>
                            <div wire:ignore>
                                <label for="state" class="form-label">State <span class="text-danger">*</span></label>
                                <select class="form-control" id="state" wire:model="practice.state">
                                    <option value="">Select State</option>
                                    @foreach ($states as $state)
                                        <option value="{{ $state->id }}"
                                            {{ $practice->state == $state->id ? 'selected' : '' }}>{{ $state->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <x-form.error model="practice.state" />                
                        </x-layout.col>

                        <x-layout.col>
                            <x-form.input.text label="Zip Code" labelRequired="1" model="practice.zip" />
                        </x-layout.col>

                        <x-layout.col>
                            <x-form.input.text label="Phone" model="practice.phone" labelRequired="0" type="number" />
                        </x-layout.col>

                        <x-layout.col>
                            <x-form.input.text label="Fax" model="practice.fax" type="number" />
                        </x-layout.col>

                        {{-- <x-layout.col>
                            <div class="form-group" wire:ignore>
                                <label for="default_order_method" class="form-label">Default Order Method</label>
                                <select wire:model="practice.default_order_method" id="default_order_method" class="form-control">
                                    <option value="">Select Method</option>
                                    <option value="{{ User::DISPATCH_METHOD_FAX }}"
                                        {{ $practice->default_order_method == User::DISPATCH_METHOD_FAX ? 'selected' : '' }}>
                                        Fax Plus
                                    </option>
                                    <option value="{{ User::DISPATCH_METHOD_DISPENSE_PRO }}"
                                        {{ $practice->default_order_method == User::DISPATCH_METHOD_DISPENSE_PRO ? 'selected' : '' }}>
                                        Dispense Pro</option>
                                </select>
                            </div>
                        </x-layout.col>

                        @if ($practice['default_order_method'] === User::DISPATCH_METHOD_DISPENSE_PRO)
                            <x-layout.col>
                                <x-form.input.text label="DispensePro Abbreviation"
                                    model="practice.dispensepro_abbreviation" labelRequired="1" />
                            </x-layout.col>
                        @endif --}}

                    </x-layout.row>
                </x-card.body>
                <x-card.footer>
                    <x-btn-with-loading target="store" text="Save" type="submit" />
                </x-card.footer>
            </div>
        </x-layout.row>
    </form>
</div>

@push('scripts')
<script>
        $(document).ready(function () {
        $('#state').select2({
            placeholder: "Select State",
        }).on('change', function (e) {
            @this.set('practice.state', $(e.target).val());
        });
        });
        document.addEventListener("livewire:load", function() {
                $(document).ready(function () {
                $('#default_order_method').select2({
                    placeholder: "Select Default Order Method",
                }).on('change', function (e) {
                    @this.set('practice.default_order_method', $(e.target).val());
                });
            });
        });

    </script>
@endpush
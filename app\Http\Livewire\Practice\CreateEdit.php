<?php

namespace App\Http\Livewire\Practice;

use App\Models\Practice;
use App\Models\State;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class CreateEdit extends Component
{
    public $providers = [];
    public $states = [];
    public $practice;
    public $user;

    public function mount(Practice $practice)
    {
        $this->user = Auth::user();
        $this->practice = $practice;
        $this->loadProvider();
        $this->loadStates();
    }

    public function rules()
    {
        return [
            'practice.name' => 'required|max:256|regex:/^[A-Za-z\s]+$/',
            'practice.address' => 'required|max:256',
            'practice.city' => 'required|max:256',
            'practice.state' => 'required',
            'practice.zip' => 'required|regex:/^\d{5}(-\d{4})?$/',
            'practice.phone' => 'nullable|max:15',
            'practice.fax' => 'nullable',
            // 'practice.default_order_method' => 'required',
            // 'practice.dispensepro_abbreviation' => 'required_if:practice.default_order_method,Dispense Pro',
        ];
    }

    public function messages()
    {
        return [
            'practice.zip.regex' => 'The ZIP code must be in the format 12345 or 12345-6789. i.e Format of 5+4.',
            'practice.name.regex' => 'The name field may only contain letters and spaces.',
        ];
    }

    public function loadProvider()
    {
        $this->providers = User::where('role', User::ROLE_PROVIDER)->get();
    }

    public function loadStates()
    {
        $this->states = State::orderBy('name')->get();
    }

    public function store()
    {
        $this->validate();

        $this->practice->save();

        session()->flash('success-message', __('messages.practice_created'));

        return redirect()->route('practices.index');
    }

    public function updated($propertyName)
    {
        // Clear validation errors as soon as user starts typing
        $this->validateOnly($propertyName);
    }

    public function render()
    {
        return view('livewire.practice.create-edit');
    }
}

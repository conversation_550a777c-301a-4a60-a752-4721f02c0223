

<?php
    use App\Models\ImportFile;
?>

<?php $__env->startSection('content'); ?>
    <div class="card card-custom mb-5">

        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row justify-content-between ">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search..." id="users_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
                <div class="col-auto">
                    <button type="button" id="download-all-global-btn" class="btn btn-dark">
                        <i class="fa fa-download mr-1"></i> Download All
                    </button>
                    <button type="button" id="download-selected-global-btn" class="btn btn-dark">
                        <i class="fa fa-download mr-1"></i> Download Selected
                    </button>
                    
                </div>

            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="script_date_filter">Script Date:</label>
                    <div class="input-group date">
                        <input type="date" class="form-control datepicker" id="script_date_filter" />
                        <div class="input-group-append">
                            <button class="btn btn-secondary" type="button" id="clear_date_filter">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="medication_filter">Medication:</label>
                    <select class="form-control" id="medication_filter">
                        <option value="">All Medications</option>
                        <?php $__currentLoopData = $medications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $medication): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($medication->id); ?>"><?php echo e($medication->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>


            <div class="datatable datatable-bordered datatable-head-custom" id="pending_import_files_search"></div>

        </div>
    </div>

    <!-- Script Preview Modal -->
    <div class="modal fade" id="scriptPreviewModal" tabindex="-1" role="dialog" aria-labelledby="scriptPreviewModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scriptPreviewModalLabel">Script Preview</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i class="ki ki-close"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="script-preview-content">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="#" id="download-preview-btn" class="btn btn-primary">
                        <i class="fas fa-download"></i> Download
                    </a>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('styles'); ?>
    <style>
        button:disabled {
            cursor: not-allowed !important;
        }

        /* Custom styles for the preview modal */
        #scriptPreviewModal .modal-dialog {
            max-width: 95%;
            height: 95vh;
            margin: 0.5rem auto;
        }

        #scriptPreviewModal .modal-content {
            height: 100%;
            border-radius: 4px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
        }

        #scriptPreviewModal .modal-body {
            flex: 1;
            overflow: hidden;
            padding: 0;
        }

        #script-preview-content {
            height: 100%;
            width: 100%;
            overflow: hidden;
            position: relative;
        }

        #script-preview-content iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
        }

        #pending_import_files_search tbody tr td:not(:last-child) {
            cursor: pointer;
        }

        #pending_import_files_search tbody tr:hover td:not(:last-child) {
            background-color: rgba(54, 153, 255, 0.1) !important;
        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        $(document).ready(function() {            
            $('#medication_filter').select2({
                width: '100%'
            });
        });
    </script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;
        // Store selected IDs to preserve them during search
        var selectedIds = [];

        // Initialize datepicker
        $(document).ready(function () {
            $('.datepicker').datepicker({
                format: 'dd/mm/yyyy',
                autoclose: true,
                todayHighlight: true,
                orientation: "bottom left"
            });
        });

        const storagePath = `<?php echo e(url('/storage')); ?>`;
        const apiRoute = `<?php echo e(route('scripts.api.provider-pending-approval')); ?>`;
        let url = "<?php echo e(Storage::url('/')); ?>";
        const sendAllRoute = `<?php echo e(route('scripts.send-for-approval')); ?>`;


        datatableElement = $('#pending_import_files_search');
        searchElement = $('#users_search');

        columnArray = [{
            field: 'checkbox',
            title: '<label class="checkbox checkbox-single checkbox-all"><input type="checkbox" id="select-all-checkbox" />&nbsp;<span></span></label>',
            sortable: false,
            width: 100,
            autoHide: false,
            textAlign: 'center',
            template: function (data) {
                return `<label class="checkbox checkbox-single">
                        <input type="checkbox" class="row-checkbox" value="${data.id}" />&nbsp;<span></span>
                    </label>`;
            }
        },
        {
            field: 'import_file_name',
            title: `File Name`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function (data) {
                return `<div style="white-space: normal; text-wrap: wrap;">${data.import_file_name ?? ''}</div>`;
            }
        },
        {
            field: 'created_at',
            title: `Created At`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function (data) {
                return data.created_at ? moment(data.created_at).format('MM/DD/YYYY hh:mm A') : '';
            }
        },
        {
            field: 'signed_at',
            title: `Signed at`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function (data) {
                return data.signed_at ? moment.parseZone(data.signed_at).format('MM/DD/YYYY hh:mm A') :
                    '<b>Not Signed Yet</b>';
            }
        },
        {
            field: 'sent_at',
            title: `Sent at`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function (data) {
                return data.sent_at ? moment(data.sent_at).format('MM/DD/YYYY hh:mm A') :
                    '<b>Not Sent Yet</b>';
            }
        },

        {
            field: 'script_date',
            title: `Script date`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function (data) {
                return moment(data.script_date).format('MM/DD/YYYY');
            }
        },
        {
            field: 'last_name',
            title: `Last name`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },
        {
            field: 'first_name',
            title: `First name`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },
        {
            field: 'medication',
            title: `Medication`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },

        {
            field: 'status',
            title: `Status`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },
        {
            field: 'Actions',
            title: 'Actions',
            sortable: false,
            width: 'auto',
            overflow: 'visible',
            autoHide: false,
            template: function (row) {
                const downloadRoute = `<?php echo e(route('archive.file-download', ['id' => '::ID'])); ?>`.replace('::ID',
                    row.id);
                const viewRoute = `<?php echo e(route('archive.show-pdf', ['id' => '::ID'])); ?>`.replace('::ID', row.id);
                const returnRoute = `<?php echo e(route('scripts.api.return-script')); ?>`;

                // const sendRoute = `<?php echo e(route('archive.sign-pdf', ['id' => '::ID'])); ?>`.replace('::ID', row.id);

                const status = row.status.toLowerCase();

                const returnBtn = `
                        <a href="#" data-id="${row.id}" data-return-route="${returnRoute}" class="btn btn-sm btn-clean btn-icon return-btn" data-toggle="tooltip" title="Recall Script">
                            <span class="menu-icon"><i class="fas fa-share"></i></span>
                        </a>`;

                return `
                            <a href="${downloadRoute}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Download Script">
                                <i class="menu-icon fas fa-download"></i>
                            </a>
                            <a href="#" data-id="${row.id}" data-view-route="${viewRoute}" data-download-route="${downloadRoute}" class="btn btn-sm btn-clean btn-icon preview-btn" data-toggle="tooltip" title="Preview Script">
                                <i class="menu-icon fas fa-eye"></i>
                            </a>
                            ${returnBtn}
                        `;
            },
        }

        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        //sample custom headers
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        params: function () {
                            // Get the current query parameters
                            const query = datatable.getDataSourceQuery();

                            // Get the current search value directly from the search input
                            const searchValue = $(searchElement).val() || '';

                            // Get the script date directly from the input field
                            const scriptDateValue = $('#script_date_filter').val();

                            // Make sure we're sending a valid date format
                            let formattedScriptDate = '';
                            if (scriptDateValue) {
                                // Parse the date value (which could be in DD/MM/YYYY format)
                                let dateObj;

                                // Check if the date is in DD/MM/YYYY format
                                if (scriptDateValue.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
                                    const parts = scriptDateValue.split('/');
                                    // Create date as MM/DD/YYYY for JS Date object
                                    dateObj = new Date(parts[2], parts[1] - 1, parts[0]);
                                } else {
                                    // Try standard date parsing
                                    dateObj = new Date(scriptDateValue);
                                }

                                if (!isNaN(dateObj.getTime())) {
                                    // Format as YYYY-MM-DD for the backend
                                    const year = dateObj.getFullYear();
                                    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                                    const day = String(dateObj.getDate()).padStart(2, '0');
                                    formattedScriptDate = `${year}-${month}-${day}`;
                                }
                            }

                            // Add selected IDs and filter parameters to the request
                            return {
                                displayed_ids: selectedIds,
                                script_date: formattedScriptDate || query.script_date || '',
                                provider_id: query.provider_id || '',
                                medication_id: query.medication_id || '',
                                // Get the current search value
                                search: searchValue,
                                // Add query structure for compatibility with backend
                                query: {
                                    script_date: formattedScriptDate || query.script_date || '',
                                    provider_id: query.provider_id || '',
                                    medication_id: query.medication_id || '',
                                    search: searchValue
                                }
                            };
                        },
                        map: function (raw) {
                            // sample data mapping
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search'
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        // Initialize the "Selected" buttons as disabled by default
        toggleSelectedButtons(true);

        // Initialize the datatable query parameters with empty values
        datatable.setDataSourceQuery({
            query: {
                script_date: '',
                provider_id: '',
                medication_id: '',
                search: ''
            }
        });

        // Function to disable/enable all buttons on the page
        function togglePageButtons(disable) {
            // Disable/enable only the "All" buttons
            $('#download-all-global-btn').prop('disabled', disable);
            $('#send-all-global-btn').prop('disabled', disable);

            // If disabling all buttons due to empty data, also disable the "Selected" buttons
            if (disable) {
                toggleSelectedButtons(true);
            }
        }

        // Function to toggle "All" buttons when items are selected
        function toggleAllButtonsWhenSelected(hasSelection) {
            // When items are selected, disable "All" buttons
            $('#download-all-global-btn').prop('disabled', hasSelection);
            $('#send-all-global-btn').prop('disabled', hasSelection);
        }

        // Function to toggle the "Selected" buttons based on whether any items are selected
        function toggleSelectedButtons(disable) {
            $('#download-selected-global-btn').prop('disabled', disable);
            $('#send-selected-global-btn').prop('disabled', disable);
        }

        // Handle clear date filter button
        $('#clear_date_filter').on('click', function () {
            // Clear the date input
            $('#script_date_filter').val('');

            // Get current filter values
            const providerId = $('#provider_filter').val();
            const medicationId = $('#medication_filter').val();

            // Get current search value - this will preserve the search text when clearing date filter
            const searchValue = $(searchElement).val() || '';

            // Set the query parameters for the datatable
            datatable.setDataSourceQuery({
                script_date: '',
                provider_id: providerId,
                medication_id: medicationId,
                search: searchValue,
                query: {
                    script_date: '',
                    provider_id: providerId,
                    medication_id: medicationId,
                    search: searchValue
                }
            });

            // Reload the datatable with the new query parameters
            datatable.reload();
        });

        $('#medication_filter').select2({
            placeholder: "Select Medication",
        });

        $('#script_date_filter, #provider_filter, #medication_filter').on('change', function () {
            // Get current filter values
            const scriptDateValue = $('#script_date_filter').val();
            const providerId = $('#provider_filter').val();
            const medicationId = $('#medication_filter').val();

            // Get current search value - this will preserve the search text when changing filters
            const searchValue = $(searchElement).val() || '';


            // Format the date properly
            let formattedScriptDate = '';
            if (scriptDateValue) {
                // Parse the date value (which could be in DD/MM/YYYY format)
                let dateObj;

                // Check if the date is in DD/MM/YYYY format
                if (scriptDateValue.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
                    const parts = scriptDateValue.split('/');
                    // Create date as MM/DD/YYYY for JS Date object
                    dateObj = new Date(parts[2], parts[1] - 1, parts[0]);
                } else {
                    // Try standard date parsing
                    dateObj = new Date(scriptDateValue);
                }

                if (!isNaN(dateObj.getTime())) {
                    // Format as YYYY-MM-DD for the backend
                    const year = dateObj.getFullYear();
                    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                    const day = String(dateObj.getDate()).padStart(2, '0');
                    formattedScriptDate = `${year}-${month}-${day}`;
                    console.log('Formatted script date:', formattedScriptDate);
                }
            }

            // Save selected IDs before reloading
            saveSelectedIds();

            // Set the query parameters for the datatable
            datatable.setDataSourceQuery({
                script_date: formattedScriptDate,
                provider_id: providerId,
                medication_id: medicationId,
                search: searchValue,
                query: {
                    script_date: formattedScriptDate,
                    provider_id: providerId,
                    medication_id: medicationId,
                    search: searchValue
                }
            });

            // Reload the datatable with the new query parameters
            datatable.reload();
        });

        // Handle ajax done event
        datatable.on('datatable-on-ajax-done', function (e, data) {
            // Check if data is empty
            const isEmpty = !data || !data.length;

            // If data is empty, disable all buttons
            if (isEmpty) {
                togglePageButtons(true);
            } else {
                // Otherwise, check if there are selections
                const hasSelectedItems = selectedIds.length > 0;

                // Toggle buttons based on selection state
                toggleAllButtonsWhenSelected(hasSelectedItems);
                toggleSelectedButtons(!hasSelectedItems);
            }

            // After data is loaded, restore selections
            setTimeout(function () {
                restoreSelectedIds();
                updateSelectAllCheckboxState();
            }, 300);
        });

        // Handle ajax fail event
        datatable.on('datatable-on-ajax-fail', function (e, jqXHR) {
            // Disable buttons on error
            togglePageButtons(true);
        });

        // Handle datatable reloads
        datatable.on('datatable-on-reloaded', function () {
            // After reload, restore selections with a delay and check if data is empty
            setTimeout(function () {
                // Check if there's data in the table
                const hasData = $('#pending_import_files_search tbody tr').length > 0;

                if (!hasData) {
                    // If no data, disable all buttons
                    togglePageButtons(true);
                } else {
                    // Restore selections and update button states
                    restoreSelectedIds();
                }
            }, 300);
        });

        // Handle datatable layout updates
        datatable.on('datatable-on-layout-updated', function () {
            // After layout update, restore selections and check if data is empty
            setTimeout(function () {
                // Check if there's data in the table
                const hasData = $('#pending_import_files_search tbody tr').length > 0;

                if (!hasData) {
                    // If no data, disable all buttons
                    togglePageButtons(true);
                } else {
                    // Restore selections and update button states
                    restoreSelectedIds();
                }
            }, 300);
        });

        // Add event listeners to search input
        $(searchElement).on('keyup search input', function (e) {
            // Save current selections before search
            saveSelectedIds();

            // Check if any checkboxes are selected
            const hasSelectedItems = selectedIds.length > 0;

            // Toggle buttons based on selection state
            toggleAllButtonsWhenSelected(hasSelectedItems);
            toggleSelectedButtons(!hasSelectedItems);
        });

        // Function to capture client device time and submit the sign form
        function captureTimeAndSign(signRoute) {
            // Get the current date
            const now = new Date();

            // Create a complete date-time string with timezone information
            // Format: YYYY-MM-DD HH:MM:SS +/-HHMM
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');

            // Get timezone offset in minutes and convert to hours and minutes
            const tzOffset = now.getTimezoneOffset();
            const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60)).toString().padStart(2, '0');
            const tzOffsetMinutes = Math.abs(tzOffset % 60).toString().padStart(2, '0');
            const tzSign = tzOffset <= 0 ? '+' : '-'; // Note: getTimezoneOffset returns negative for positive UTC offsets

            // Construct the full datetime string with timezone
            const clientTimestamp =
                `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${tzSign}${tzOffsetHours}${tzOffsetMinutes}`;


            // Create a form to submit
            const form = $('<form>', {
                method: 'GET',
                action: signRoute
            });

            // Add client timestamp as a query parameter
            form.append($('<input>', {
                type: 'hidden',
                name: 'client_timestamp',
                value: clientTimestamp
            }));

            // Submit the form
            $('body').append(form);
            form.submit();
            form.remove();
        }

        $(document).on('click', '.sent-btn', function (e) {
            e.preventDefault();
            const sendAllRoute = $(this).data('sent-route');
            captureTimeAndSign(sendAllRoute);
        });

        const routeTemplate = "<?php echo e(route('scripts.download-all-pdf')); ?>";

        $('#download-all-global-btn').on('click', function () {
            const form = $('<form>', {
                method: 'POST',
                action: routeTemplate
            });

            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '<?php echo e(csrf_token()); ?>'
            }));

            // Determine the status based on the API route being used
            const statusValue = apiRoute.includes('ready-to-send') ? '<?php echo e(ImportFile::STATUS_SIGNED); ?>' :
                '<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>';

            form.append($('<input>', {
                type: 'hidden',
                name: 'status[]',
                value: statusValue // Dynamically set based on the API route
            }));

            // Get current filter values and add them to the form
            const scriptDate = $('#script_date_filter').val();
            if (scriptDate) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'script_date',
                    value: scriptDate
                }));
            }

            const medicationId = $('#medication_filter').val();
            if (medicationId) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'medication_id',
                    value: medicationId
                }));
            }

            // Get current search value
            const searchValue = $(searchElement).val();
            if (searchValue) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'search',
                    value: searchValue
                }));
            }

            $('body').append(form);
            form.submit();
            form.remove();
        });

        $('#download-selected-global-btn').on('click', function () {
            const selectedIds = getSelectedIds();

            if (selectedIds.length === 0) {
                return;
            }

            const form = $('<form>', {
                method: 'POST',
                action: routeTemplate.replace('__ID__', '') // same endpoint
            });

            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '<?php echo e(csrf_token()); ?>'
            }));

            // Determine the status based on the API route being used
            const statusValue = apiRoute.includes('ready-to-send') ? '<?php echo e(ImportFile::STATUS_SIGNED); ?>' :
                '<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>';

            // Pass status filter
            form.append($('<input>', {
                type: 'hidden',
                name: 'status[]',
                value: statusValue // Dynamically set based on the API route
            }));

            // Add displayed_ids[] inputs
            selectedIds.forEach(function (id) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'displayed_ids[]',
                    value: id
                }));
            });

            $('body').append(form);
            form.submit();
            form.remove();
        });

        $('#send-all-global-btn').on('click', function () {
            const form = $('<form>', {
                method: 'POST',
                action: sendAllRoute
            });

            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '<?php echo e(csrf_token()); ?>'
            }));

            // Determine the status based on the API route being used
            const currentStatus = apiRoute.includes('ready-to-send') ? '<?php echo e(ImportFile::STATUS_SIGNED); ?>' :
                '<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>';
            const targetStatus = apiRoute.includes('ready-to-send') ? '<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>' :
                '<?php echo e(ImportFile::STATUS_SENT); ?>';

            // Pass current status
            form.append($('<input>', {
                type: 'hidden',
                name: 'status',
                value: currentStatus // Dynamically set based on the API route
            }));

            // Pass target status
            form.append($('<input>', {
                type: 'hidden',
                name: 'changed_status',
                value: targetStatus // Dynamically set based on the API route
            }));

            $('body').append(form);
            form.submit();
            form.remove();
        });

        $('#send-selected-global-btn').on('click', function () {
            const selectedIds = getSelectedIds();

            if (selectedIds.length === 0) {
                return;
            }

            const form = $('<form>', {
                method: 'POST',
                action: sendAllRoute
            });

            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '<?php echo e(csrf_token()); ?>'
            }));

            // Determine the status based on the API route being used
            const currentStatus = apiRoute.includes('ready-to-send') ? '<?php echo e(ImportFile::STATUS_SIGNED); ?>' :
                '<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>';
            const targetStatus = apiRoute.includes('ready-to-send') ? '<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>' :
                '<?php echo e(ImportFile::STATUS_SENT); ?>';

            // Pass current status
            form.append($('<input>', {
                type: 'hidden',
                name: 'status',
                value: currentStatus // Dynamically set based on the API route
            }));

            // Pass target status
            form.append($('<input>', {
                type: 'hidden',
                name: 'changed_status',
                value: targetStatus // Dynamically set based on the API route
            }));

            // Add displayed_ids[] inputs
            selectedIds.forEach(function (id) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'displayed_ids[]',
                    value: id
                }));
            });

            $('body').append(form);
            form.submit();
            form.remove();
        });

        // Handle "Select All" checkbox
        $(document).on('change', '#select-all-checkbox', function () {
            let isChecked = $(this).is(':checked');

            // Check/uncheck all visible checkboxes
            $('.row-checkbox').prop('checked', isChecked);

            // Use our improved getSelectedIds function to update the global selection state
            getSelectedIds();

            // Update button states based on selection
            if (isChecked && $('.row-checkbox').length > 0) {
                toggleAllButtonsWhenSelected(true);
                toggleSelectedButtons(false);
            } else if (!isChecked) {
                // If there are still selected items on other pages
                if (selectedIds.length > 0) {
                    toggleAllButtonsWhenSelected(true);
                    toggleSelectedButtons(false);
                } else {
                    // If nothing is selected anywhere
                    toggleAllButtonsWhenSelected(false);
                    toggleSelectedButtons(true);
                }
            }

            console.log('Updated selected IDs after select all/none:', selectedIds);
        });

        // Handle individual checkbox changes
        $(document).on('change', '.row-checkbox', function () {
            // Use our improved getSelectedIds function to update the global selection state
            getSelectedIds();

            // Update the "Select All" checkbox state
            updateSelectAllCheckboxState();

            console.log('Updated selected IDs after individual checkbox change:', selectedIds);
        });

        // Function to update the "Select All" checkbox state
        function updateSelectAllCheckboxState() {
            const totalCheckboxes = $('.row-checkbox').length;
            const checkedCheckboxes = $('.row-checkbox:checked').length;

            if (totalCheckboxes > 0 && totalCheckboxes === checkedCheckboxes) {
                $('#select-all-checkbox').prop('checked', true);
            } else {
                $('#select-all-checkbox').prop('checked', false);
            }
        }

        function getSelectedIds() {
            // Get all visible checkbox IDs on the current page
            let visibleIds = [];
            $('.row-checkbox').each(function () {
                visibleIds.push($(this).val());
            });

            // First, remove any IDs from selectedIds that are visible on the current page
            // This ensures we don't have stale selections
            selectedIds = selectedIds.filter(function (id) {
                return !visibleIds.includes(id);
            });

            // Now add all currently checked IDs to selectedIds
            $('.row-checkbox:checked').each(function () {
                const id = $(this).val();
                selectedIds.push(id);
            });

            // Check if any checkboxes are selected
            const hasSelectedItems = selectedIds.length > 0;

            // Toggle buttons based on selection state
            toggleAllButtonsWhenSelected(hasSelectedItems);
            toggleSelectedButtons(!hasSelectedItems);

            return selectedIds;
        }

        // Function to save selected IDs
        function saveSelectedIds() {
            // Use our improved getSelectedIds function to update the global selection state
            getSelectedIds();
            console.log('Updated selected IDs:', selectedIds);
        }

        // Function to restore selected IDs
        function restoreSelectedIds() {
            console.log('Restoring selections for IDs:', selectedIds);

            // First uncheck all checkboxes
            $('.row-checkbox').prop('checked', false);

            // Then check only those that were previously selected
            let restoredCount = 0;
            $('.row-checkbox').each(function () {
                const id = $(this).val();
                if (id && selectedIds.includes(id)) {
                    $(this).prop('checked', true);
                    restoredCount++;
                }
            });

            // Update the "Select All" checkbox state
            updateSelectAllCheckboxState();

            // Check if any checkboxes are selected
            const hasSelectedItems = selectedIds.length > 0;

            // Toggle buttons based on selection state
            toggleAllButtonsWhenSelected(hasSelectedItems);
            toggleSelectedButtons(!hasSelectedItems);

            console.log('Restored', restoredCount, 'selections');
        }

        // Add event listener for preview buttons
        $(document).on('click', '.preview-btn', function (e) {
            e.preventDefault();

            const fileId = $(this).data('id');
            const viewRoute = $(this).data('view-route');
            const downloadRoute = $(this).data('download-route');

            // Set the download button URL
            $('#download-preview-btn').attr('href', downloadRoute);

            // Show the modal
            $('#scriptPreviewModal').modal('show');

            // Load the script preview
            $('#script-preview-content').html(
                '<div class="text-center"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>'
            );

            // Load the PDF in an iframe
            setTimeout(function () {
                $('#script-preview-content').html(
                    `<iframe src="${viewRoute}" width="100%" height="100%" style="height: 90vh;" frameborder="0"></iframe>`
                );
            }, 500);
        });

        // Handle modal events
        $('#scriptPreviewModal').on('hidden.bs.modal', function () {
            // Clear the preview content when modal is closed
            $('#script-preview-content').html('');
        });

        // Simple row click handler
        $(document).on('click', '#pending_import_files_search tbody tr td', function (e) {
            // Skip if clicking on the checkbox cell or actions cell
            if ($(this).is(':first-child') || $(this).is(':last-child') ||
                $(e.target).is('input[type="checkbox"]') ||
                $(e.target).closest('a').length ||
                $(e.target).closest('button').length ||
                $(e.target).closest('i').length) {
                return;
            }

            // Find the checkbox in the first cell
            const checkbox = $(this).closest('tr').find('td:first-child input[type="checkbox"]');

            // Toggle the checkbox
            checkbox.prop('checked', !checkbox.prop('checked'));

            // Trigger change event
            checkbox.trigger('change');
        });
        
        $(document).on('click', '.return-btn', function (e) {
            e.preventDefault();
            const scriptId = $(this).data('id');
            const returnRoute = $(this).data('return-route');

            Swal.fire({
                title: 'Are you sure?',
                text: "Do you want to recall this script?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, recall it!',
                cancelButtonText: 'No, cancel',
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: returnRoute,
                        method: 'POST',
                        data: {
                            _token: '<?php echo e(csrf_token()); ?>',
                            id: scriptId
                        },
                        success: function (res) {
                            Swal.fire('Recalled!', res.message || 'Script recalled successfully.', 'success');
                            datatable.reload();
                        },
                        error: function (xhr) {
                            let message = 'Failed to recall script.';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                message = xhr.responseJSON.message;
                            }
                            Swal.fire('Error', message, 'error');
                        }
                    });
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/scripts/index-provider-pending-approval.blade.php ENDPATH**/ ?>
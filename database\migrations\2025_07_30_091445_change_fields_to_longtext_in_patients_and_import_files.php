<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->longText('first_name')->nullable()->change();
            $table->longText('last_name')->nullable()->change();
            $table->longText('address')->nullable()->change();
            $table->longText('city')->nullable()->change();
        });

        Schema::table('import_files', function (Blueprint $table) {
            $table->longText('first_name')->nullable()->change();
            $table->longText('last_name')->nullable()->change();
            $table->longText('address')->nullable()->change();
            $table->longText('city')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->string('first_name', 255)->nullable()->change();
            $table->string('last_name', 255)->nullable()->change();
            $table->string('address', 255)->nullable()->change();
            $table->string('city', 255)->nullable()->change();
        });

        Schema::table('import_files', function (Blueprint $table) {
            $table->string('first_name', 255)->nullable()->change();
            $table->string('last_name', 255)->nullable()->change();
            $table->string('address', 255)->nullable()->change();
            $table->string('city', 255)->nullable()->change();
        });
    }
};

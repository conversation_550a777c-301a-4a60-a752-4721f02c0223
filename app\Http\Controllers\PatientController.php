<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\GetDatatableRequest;
use App\Http\Resources\DataTableCollection;
use Illuminate\Http\Request;
use App\Models\Patient;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use App\Models\ImportFile;

class PatientController extends Controller
{
    public function index()
    {
        $page_title = 'Patients';

        return view('patients.index', [
            'page_title' => $page_title,
        ]);
    }

    public function indexWeb(GetDatatableRequest $request)
    {
        $user = Auth::user();

        if ($user->role == User::ROLE_PROVIDER) {
            $data = Patient::where('provider_id', $user->id)->with(['provider', 'state'])->latest();
        } else {
            $data = Patient::query()->with(['provider', 'state']);
        }

        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');

        if ($search) {
            $query_search = "%" . $search . "%";
            $data->where(function ($query) use ($query_search) {
                $query->where('first_name', 'like', $query_search)
                    ->orWhere('last_name', 'like', $query_search)
                    ->orWhere('address', 'like', $query_search)
                    ->orWhereRaw("DATE_FORMAT(dob, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhere('phone_number', 'like', $query_search)
                    // ->orWhere('city', 'like', $query_search)
                    // ->orWhere('zip_code', 'like', $query_search)
                    // ->orWhere('gender', 'like', $query_search)
                    // ->orWhereHas('state', function ($query) use ($query_search) {
                    //     $query->where('name', 'like', $query_search);
                    // })
                    // ->orWhereHas('provider', function ($query) use ($query_search) {
                    //     $query->where('first_name', 'like', $query_search)
                    //         ->orWhere('last_name', 'like', $query_search)
                    //         ->orWhere('email', 'like', $query_search);
                    // })
                ;
            });
        }

        if ($sort_order && $sort_field) {
            $patientColumns = Schema::getColumnListing((new Patient())->table);

            if (in_array($sort_field, $patientColumns)) {
                $data->orderBy($sort_field, $sort_order);
            } else {
                $data->latest();
            }
        } else {
            $data->latest();
        }
        return new DataTableCollection($data->paginate($request->pagination['perpage'], ['*'], 'page', $request->pagination['page']));
    }

    public function create()
    {
        $has_back = route('patients.index');
        $page_title = 'Create Patient';

        $livewire_component = 'patient.create-edit';
        $livewire_data = [
            'page_title' => $page_title,
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    public function edit(Patient $patient)
    {
        $has_back = route('patients.index');
        $page_title = 'Edit Patient';

        $livewire_component = 'patient.create-edit';
        $livewire_data = [
            'patient' => $patient,
            'page_title' => $page_title,
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    public function delete(Patient $patient)
    {
        $patient->delete();
        return response([
            'message' => __('messages.patient_deleted'),
            'status' => '1',
        ]);
    }

    public function PatientScripts($patientId)
    {
        $page_title = 'Patient Scripts';
        $has_back = route('patients.index');

        return view('patients.scripts', [
            'page_title' => $page_title,
            'patientId' => $patientId,
            'has_back' => $has_back,
        ]);
    }

    public function PatientScriptsApi(Request $request)
    {
        $patientId = $request->patientId;
        // dd($patientId);
        $data = ImportFile::where('patient_id', $patientId)->with('import');

        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');

        if ($search) {
            $query_search = "%" . $search . "%";

            $data->where(function ($query) use ($query_search) {
                $query->where('first_name', 'like', $query_search)
                    ->orWhere('last_name', 'like', $query_search)
                    ->orWhere('medication', 'like', $query_search)
                    ->orWhereRaw("DATE_FORMAT(script_date, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(signed_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(sent_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereHas('import', function ($query) use ($query_search) {
                        $query->where('file_name', 'like', $query_search);
                    });
            });
        }

        if ($sort_order && $sort_field) {
            $patientColumns = Schema::getColumnListing((new Patient())->table);

            if (in_array($sort_field, $patientColumns)) {
                $data->orderBy($sort_field, $sort_order);
            } else {
                $data->latest();
            }
        } else {
            $data->latest();
        }
        return new DataTableCollection($data->paginate($request->pagination['perpage'], ['*'], 'page', $request->pagination['page']));
    }
}

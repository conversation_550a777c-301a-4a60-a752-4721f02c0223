<?php

namespace App\Http\Livewire\Medication;

use Livewire\Component;
use App\Models\Medication;
use Illuminate\Validation\Rule;

class CreateEdit extends Component
{
    public Medication $medication;

    public function mount(Medication $medication)
    {
        $this->medication = $medication;
    }

    public function rules()
    {
        $rules = [
            'medication.name' => $this->medication->id ? 'required|string|max:255|unique:medications,name,' . $this->medication->id : 'required|string|max:255|unique:medications,name',
            'medication.ndc' => $this->medication->id ? 'required|string|max:255|unique:medications,ndc,' . $this->medication->id : 'required|string|max:255|unique:medications,ndc',
            'medication.dispensepro_medication_name' => 'required|string|max:255',
            'medication.refills' => 'required|numeric|min:0|max:24',
            'medication.vial_quantity' => 'required|numeric|min:1|max:32',
            'medication.days_supply' => 'required|numeric|min:28|max:366',
            'medication.sig' => 'nullable|string|max:1024',
            'medication.notes' => 'nullable|string|max:1024',
            'medication.default_dispatch_method' => 'required'

        ];
        // Conditionally add rule for dispense_abbreviation
        if ($this->medication['default_dispatch_method'] === 'Dispense Pro') {
            $rules['medication.dispense_abbreviation'] = [
                'required',
                Rule::unique('medications', 'dispense_abbreviation')->ignore($this->medication['id'] ?? null),
            ];
        }
        return $rules;
    }

    public function messages()
    {
        return [
            'medication.name.required' => 'The medication title field is required.',
            'medication.name.unique' => 'The medication title has already been taken.',
            'medication.dispense_abbreviation.unique' => 'The dispense abbreviation must be unique.',

        ];
    }


    public function update($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $this->validate();

        // Clear dispense abbreviation if dispatch method is FAX
        if ($this->medication->default_dispatch_method == Medication::DISPATCH_METHOD_FAX) {
            $this->medication->dispense_abbreviation = null;
            $this->medication->save();
        }

        if (!$this->medication->id) {
            // Save the medication to the database

            $this->medication->save();

            $message = __('messages.medication_created');
        } else {
            // Update the existing medication
            $this->medication->save();

            // Optionally, you can add a success message or redirect
            $message = __('messages.medication_updated');
        }

        session()->flash('success-message', $message);

        return redirect()->route('medications.index');
    }

    public function render()
    {
        return view('livewire.medication.create-edit');
    }
}

@extends('master')

@php
    use App\Models\User;
    $user = Auth::user();
@endphp
@section('content')
    <div class="card card-custom mb-5">

        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row justify-content-between ">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search..." id="practices_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>

                @if ($user->role != User::ROLE_PROVIDER)
                    <div class="col-12 col-sm">
                        <a href="{{ route('practices.create') }}">
                            <button type="button" class="btn btn-primary" id="add-box-btn">
                                <i class="fa fa-plus"></i>
                                <span>Add Practice</span>
                            </button>
                        </a>
                    </div>
                @endif
            </div>

            <div class="datatable datatable-bordered datatable-head-custom" id="practices_dt"></div>
        </div>
    </div>
@endsection
@section('styles')
@endsection
@section('scripts')
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const storagePath = `{{ url('/storage') }}`;
        const apiRoute = `{{ route('practices.api') }}`;
        let url = "{{ Storage::url('/') }}";
        const csrfToken = `{{ csrf_token() }}`;
        const editRoute = `{{ route('practices.edit', '::ID') }}`;
        const providersRoute = `{{ route('practices.providers', '::ID') }}`;
        const medicationRoute = `{{ route('practices.medications', ['practice' => '::ID']) }}`;
        const statusChangeRoute = `{{ route('practices.status-change', ['practice' => '::ID']) }}`;
        const deleteRoute = `{{ route('practices.delete', ['practice' => '::ID']) }}`;


        datatableElement = $('#practices_dt');
        searchElement = $('#practices_search');

        columnArray = [{
            field: 'name',
            title: `Name`,
            width: 200,
            sortable: true,
            autoHide: false,
        },
        {
            field: 'city',
            title: `City`,
            width: 200,
            sortable: true,
            autoHide: false,
        },
        {
            field: 'phone',
            title: `Phone`,
            width: 200,
            sortable: true,
        },
        {
            field: 'is_active',
            title: 'Active',
            overflow: 'visible',
            autoHide: false,
            width: 'auto',
            template: function (row) {
                return `
                                        <span class="switch switch-sm switch-primary">
                                            <label>
                                                <input type="checkbox" data-id="${row.id}" class="status-change" ${row.is_active ? '' : 'checked'} name="${row.id}"/>
                                                <span></span>
                                            </label>
                                        </span>
                                    `;
            }
        },
        {
            field: 'Actions',
            title: 'Actions',
            sortable: false,
            width: 'auto',
            overflow: 'visible',
            autoHide: false,
            template: function (data) {
                return `
                                        <form method="POST" action="{{ route('practices.tokens') }}" style="display:inline;">
                                            <input type="hidden" name="_token" value="${csrfToken}">
                                            <input type="hidden" name="practice" value="${data.id}">
                                            <button type="submit" class="btn btn-sm btn-clean btn-icon " data-toggle="tooltip" title="Tokens">
                                                <i class="menu-icon fas fa-key"></i>
                                            </button>
                                        </form>
                                        @if ($user->role != User::ROLE_PROVIDER)

                                            <a href="${editRoute.replace('::ID', data.id)}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Edit Practice">
                                                <i class="menu-icon fas fa-pen"></i>
                                            </a>
                                            <a href="${medicationRoute.replace('::ID', data.id)}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Medications">
                                                <i class="menu-icon fas fa-prescription-bottle"></i>
                                            </a>
                                            <a href="${providersRoute.replace('::ID', data.id)}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="View Providers">
                                                <i class="menu-icon fas fa-user-md"></i>
                                            </a>
                                            <a href="${deleteRoute.replace('::ID', data.id)}" class="btn btn-sm btn-clean btn-icon delete-btn" data-toggle="tooltip" title="Delete Practice" data-id="${data.id}">
                                                <i class="menu-icon fas fa-trash"></i>
                                            </a>
                                        @endif

                                    `;
            },
        }
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        map: function (raw) {
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search'
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        datatableElement.on('click', '.status-change', function () {
            let id = $(this).data('id');
            let val = $(this).is(":checked") ? 1 : 0;
            let text = val == 1 ? 'Activate' : 'Deactivate';
            Swal.fire({
                title: 'Are you sure?',
                text: `${text} practice?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: `Yes, ${text}!`
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: statusChangeRoute.replace('::ID', id),
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                    })
                        .done(function (res) {
                            toastr.success(res.message);
                            datatable.reload();
                        });
                }
                else {
                    $(this).prop('checked', !val);
                }
            });
        });

        $(document).on('click', '.delete-btn', function (e) {
            e.preventDefault();

            let id = $(this).data('id');
            Swal.fire({
                title: 'Are you sure?',
                text: "You want to Delete this Practice?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, Delete the Practice!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: deleteRoute.replace('::ID', id),
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        success: function(res) {
                            toastr.success(res.message);
                            datatable.reload();
                        }
                    });
                }
            });
        });

    </script>
@endsection
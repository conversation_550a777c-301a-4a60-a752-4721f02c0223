@php
    use App\Models\Medication;
@endphp
<div>
    <form wire:submit.prevent="store">
        <x-layout.row>
            <div class="col">
                <x-card.body>
                    <div class="row">

                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="Medication Title" labelRequired="1" model="medication.name" />
                        </div>

                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="NDC (National Drug Code)" labelRequired="1"
                                model="medication.ndc" />
                        </div>

                        <div class="col-md-12 form-group">
                            <div wire:ignore>
                                <label for="default_dispatch_method">Default dispatch method</label>
                                <select label="Default Dispatch Method" labelRequired="1"
                                    model="medication.default_dispatch_method" id="default_dispatch_method">
                                    <option value="">Select Method</option>
                                    <option value="{{ Medication::DISPATCH_METHOD_FAX }}"
                                        {{ $medication->default_dispatch_method == Medication::DISPATCH_METHOD_FAX ? 'selected' : '' }}>
                                        Fax Plus
                                    </option>
                                    <option value="{{ Medication::DISPATCH_METHOD_DISPENSE_PRO }}"
                                        {{ $medication->default_dispatch_method == Medication::DISPATCH_METHOD_DISPENSE_PRO ? 'selected' : '' }}>
                                        Dispense Pro</option>
                                </select>
                            </div>
                            <x-form.error model="medication.default_dispatch_method" />
                        </div>

                        @if ($medication['default_dispatch_method'] === Medication::DISPATCH_METHOD_DISPENSE_PRO)
                            <div class="col-md-12 mb-3">
                                <x-form.input.text label="DispensePro abbreviation" labelRequired="1"
                                    model="medication.dispense_abbreviation" />
                            </div>
                        @endif
                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="DispensePro Medication Name" labelRequired="1"
                                model="medication.dispensepro_medication_name" />
                        </div>
                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="Refills" labelRequired="1" model="medication.refills"
                                type="number" class="limit-5-digits" />
                        </div>
                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="Vial Quantity" labelRequired="1" model="medication.vial_quantity"
                                type="number" class="limit-5-digits"/>
                        </div>
                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="Days Supply" labelRequired="1" model="medication.days_supply"
                                type="number" class="limit-5-digits"/>
                        </div>


                        <!-- Additional Information -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <x-form.input.textarea label="SIG" labelRequired="0" model="medication.sig" />
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <x-form.input.textarea label="Note" labelRequired="0" model="medication.notes"
                                    rows="3" />
                            </div>
                        </div>
                    </div>
                </x-card.body>

                <x-group.errors />

                <x-card.footer>
                    <x-btn-with-loading target="store" text="Save" type="submit" />
                </x-card.footer>
            </div>
        </x-layout.row>
    </form>
</div>

@push('scripts')
    <script>
        document.addEventListener("livewire:load", function() {
            createDispatchMethodDropdown()

            function createRoleDropdown() {
                $('#medication\\.role').select2({
                    placeholder: "Select Access Type",
                    minimumResultsForSearch: Infinity, // Disable the search functionality
                }).on('change', function(e) {
                    @this.set('medication.role', $(e.target).val());
                });
            }

            function createDispatchMethodDropdown() {
                let el = document.getElementById('default_dispatch_method');
                console.log("asas", el)

                if (!el) return;

                $(el).select2({
                    placeholder: "Select Method",
                    width: '100%'
                }).on('change', function(e) {
                    @this.set('medication.default_dispatch_method', e.target.value);
                });
            }


            createStateDropdown();
            createDispatchMethodDropdown();

            // Re-initialize Select2 after Livewire updates the DOM
            window.livewire.on('contentChanged', function() {
                createRoleDropdown();
                createDispatchMethodDropdown();
            });
        });

        document.addEventListener('livewire:load', function() {
            $(document).on('input', '.limit-5-digits', function() {
                let val = $(this).val().replace(/\D/g, '');
                if (val.length > 5) {
                    val = val.slice(0, 5);
                }
                $(this).val(val);
            });
        });
    </script>
@endpush
